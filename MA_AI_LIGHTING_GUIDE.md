# 🎭 MA控台专业AI灯光系统

## 📋 系统概述

这是一个专为GrandMA2/MA3控台设计的专业AI灯光系统，支持：

- **MA XML灯具库解析** - 直接导入MA控台的XML灯具库文件
- **AI音乐分析** - 智能分析音乐节拍、风格、情绪
- **智能灯光序列生成** - 基于音乐特征自动生成灯光效果
- **Art-Net实时输出** - 直接输出到MA控台进行实时控制
- **专业灯具配接** - 支持复杂的灯具配接和Universe管理

---

## 🚀 快速开始

### 1. 启动系统

```bash
cd "c:\Users\<USER>\Documents\augment-projects\MYLightToo"
python ma_ai_lighting_system.py
```

### 2. 基本工作流程

1. **📚 导入MA XML灯具库** → 2. **🎵 音乐AI分析** → 3. **🔌 灯具配接** → 4. **🤖 AI序列生成** → 5. **🔗 Art-Net连接** → 6. **▶️ 实时播放**

---

## 📚 第一步：MA XML灯具库管理

### 导入MA控台灯具库

1. **获取MA XML文件**：
   - 从MA控台导出灯具库：`File → Export → Fixture Library`
   - 或使用系统自带的示例文件：`sample_ma_fixtures.xml`

2. **导入步骤**：
   - 点击"📁 选择MA XML文件"
   - 选择您的MA XML灯具库文件
   - 点击"🔍 解析灯具库"

3. **验证导入**：
   - 查看灯具列表显示所有导入的灯具
   - 检查制造商、通道数等信息
   - 使用搜索和筛选功能查找特定灯具

### 支持的MA XML格式

- **GrandMA2格式**：`<MA><Info><FixtureType>`
- **GrandMA3格式**：`<MA><FixtureTypes><FixtureType>`
- **通用格式**：`<FixtureLibrary><Fixture>`

### 灯具库功能

- **🔍 智能搜索**：按名称、制造商搜索
- **🏷️ 制造商筛选**：快速筛选特定品牌
- **👁️ 详情查看**：双击查看完整灯具信息
- **💾 格式转换**：导出为JSON格式

---

## 🎵 第二步：AI音乐分析

### 支持的音频格式

- **主要格式**：MP3, WAV, FLAC, M4A, AAC
- **推荐质量**：192kbps以上，无噪音干扰

### AI分析功能

1. **节拍检测**：
   - 自动检测BPM（每分钟节拍数）
   - 精确定位每个节拍时间点
   - 支持复杂节拍和变速音乐

2. **风格识别**：
   - 慢板/抒情、中板/流行、快板/摇滚、电子/舞曲
   - 基于BPM和音频特征自动分类

3. **情绪分析**：
   - 激昂/兴奋、平静/舒缓、动感/活跃、稳定/平衡
   - 分析音频能量分布和频谱特征

4. **能量分布**：
   - 每10秒分析一次能量级别
   - 为灯光强度变化提供依据

### 分析结果

- **基本信息**：BPM、时长、节拍数、风格、情绪
- **详细数据**：节拍时间点、能量分布图表
- **可视化显示**：能量条形图、节拍标记

---

## 🔌 第三步：专业灯具配接

### 配接参数

1. **灯具选择**：
   - 从已导入的MA灯具库中选择
   - 显示格式：`制造商 - 灯具名称`

2. **地址设置**：
   - **起始地址**：DMX起始地址（1-512）
   - **Universe**：Art-Net Universe编号
   - **自动递增**：添加灯具后自动计算下一个地址

3. **区域分配**：
   - 舞台前区、舞台后区、观众席、背景区、特效区、侧光区
   - 支持自定义区域名称

4. **模式选择**：
   - 根据灯具定义自动显示可用模式
   - 不同模式对应不同的通道数

### 配接管理

- **📋 配接列表**：显示所有已配接灯具的详细信息
- **🔍 地址冲突检测**：自动检测并防止地址重叠
- **📊 统计信息**：总灯具数、通道数、Universe数
- **🗑️ 批量操作**：删除、编辑、复制、清空

### 配接技巧

1. **地址规划**：
   - 按区域分配地址段
   - 预留扩展空间
   - 记录地址分配表

2. **Universe分配**：
   - 每个Universe最多512个通道
   - 按功能或区域分配Universe
   - 考虑网络负载均衡

---

## 🤖 第四步：AI序列生成

### AI参数设置

1. **创意级别**：
   - **保守**：传统的灯光效果，稳定可靠
   - **中等**：平衡创新和稳定性
   - **创新**：更多创意效果和颜色组合
   - **极致**：最大化创意，实验性效果

2. **同步精度**：
   - **低**：每4拍变化一次
   - **中**：每2拍变化一次
   - **高**：每拍都有变化
   - **极高**：亚拍级精确同步

3. **效果强度**：0.1-1.0，控制整体灯光强度

4. **色彩丰富度**：0.1-1.0，控制颜色饱和度和变化

### AI生成算法

1. **风格适配**：
   - 根据音乐风格选择基础效果模板
   - 慢板→柔和渐变，快板→强烈对比

2. **情绪映射**：
   - 激昂→红色系+高强度
   - 平静→蓝色系+低强度
   - 动感→彩色循环+快速变化

3. **能量跟随**：
   - 灯光强度跟随音乐能量变化
   - 高能量段增加特效和移动

4. **智能分区**：
   - 不同区域的灯具协调配合
   - 前区主导，后区配合，特效区点缀

### 序列优化

- **节拍同步**：精确到毫秒级的节拍对齐
- **平滑过渡**：避免突兀的颜色和强度跳变
- **层次感**：主光、辅光、效果光的层次配合
- **呼吸感**：模拟自然的明暗变化节奏

---

## 🔗 第五步：Art-Net连接

### 连接设置

1. **目标IP地址**：
   - **MA2控台**：通常为 `*********` 或 `**********`
   - **MA3控台**：根据网络配置设定
   - **本地测试**：`127.0.0.1`

2. **端口设置**：
   - **标准端口**：`6454`（Art-Net标准）
   - 确保防火墙允许此端口

3. **网络配置**：
   - 确保计算机和MA控台在同一网段
   - 检查网络连通性：`ping *********`

### MA控台设置

1. **MA2设置**：
   ```
   Setup → Network → Art-Net
   - Enable Art-Net Input: Yes
   - Art-Net Universe: 设置对应的Universe
   ```

2. **MA3设置**：
   ```
   Menu → Network → Protocols → Art-Net
   - Enable: Yes
   - Input Universe: 配置输入Universe
   ```

### 连接验证

- **🟢 连接成功**：显示绿色状态，可以开始播放
- **🔴 连接失败**：检查IP地址、网络连接、防火墙设置

---

## ▶️ 第六步：实时播放控制

### 播放功能

1. **▶️ 播放**：开始播放AI生成的灯光序列
2. **⏸️ 暂停**：暂停播放，可恢复
3. **⏹️ 停止**：停止播放并清除所有输出

### 实时监控

1. **播放状态**：
   - 当前播放时间
   - 总时长和进度条
   - 当前BPM显示

2. **系统状态**：
   - Art-Net连接状态
   - 发送数据包计数
   - 活跃灯具数量

3. **活跃灯具**：
   - 实时显示正在工作的灯具
   - 显示Universe、地址、强度、颜色
   - 状态指示：活跃/待机

### 性能优化

- **50Hz更新频率**：确保流畅的灯光变化
- **智能数据压缩**：只发送变化的数据
- **多Universe支持**：自动管理多个Universe的数据

---

## 🎯 高级功能

### 序列导出

1. **JSON格式**：完整的序列数据，包含所有参数
2. **MA2格式**：兼容MA控台的XML格式（简化）
3. **配置保存**：保存灯具配接和AI参数

### 批量操作

- **灯具复制**：快速复制相似的灯具配接
- **区域操作**：按区域批量修改参数
- **模板保存**：保存常用的配接模板

### 故障排除

1. **连接问题**：
   - 检查网络连接
   - 验证IP地址和端口
   - 确认防火墙设置

2. **播放问题**：
   - 确认序列已生成
   - 检查Art-Net连接
   - 验证灯具配接

3. **性能问题**：
   - 减少同时活跃的灯具数量
   - 降低同步精度
   - 优化网络带宽

---

## 💡 最佳实践

### 工作流程建议

1. **准备阶段**：
   - 提前准备MA XML灯具库
   - 选择高质量的音频文件
   - 规划灯具布局和地址分配

2. **配接阶段**：
   - 按功能区域分组配接
   - 预留地址空间用于扩展
   - 记录详细的配接文档

3. **生成阶段**：
   - 从保守参数开始测试
   - 逐步调整AI参数
   - 多次生成对比效果

4. **播放阶段**：
   - 先在测试环境验证
   - 确认所有灯具响应正常
   - 准备备用方案

### 性能优化建议

- **硬件要求**：4GB RAM，双核CPU，千兆网卡
- **网络优化**：使用有线连接，避免WiFi延迟
- **系统优化**：关闭不必要的后台程序

---

## 🔧 技术规格

### 支持的协议

- **Art-Net v4**：标准的DMX over Ethernet协议
- **Universe支持**：理论上支持32768个Universe
- **通道精度**：8-bit (0-255) DMX值

### 文件格式支持

- **输入**：MA XML, MP3, WAV, FLAC, M4A, AAC
- **输出**：JSON, XML, CSV配置文件

### 系统兼容性

- **操作系统**：Windows 10/11, macOS, Linux
- **Python版本**：3.8+
- **MA控台**：GrandMA2, GrandMA3, MA onPC

---

## 🎉 开始您的AI灯光之旅！

这个系统将传统的灯光编程工作转变为智能化的创作过程。通过AI分析音乐特征，自动生成专业级的灯光效果，让您专注于创意而不是繁琐的编程工作。

**立即体验MA控台专业AI灯光系统，开启智能灯光控制的新时代！** 🚀
