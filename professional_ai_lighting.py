#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业AI灯光控制系统
音乐导入 → 灯具配接 → AI分析 → Art-Net输出 → Depence预览
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import socket
import struct
import threading
import time
import random
from datetime import datetime
from typing import Dict, List, Optional

class FixtureLibrary:
    """灯具库管理"""
    
    def __init__(self):
        self.fixtures = {}
        self.load_default_fixtures()
    
    def load_default_fixtures(self):
        """加载默认灯具库"""
        self.fixtures = {
            "moving_head_spot": {
                "name": "电脑摇头聚光灯",
                "channels": {
                    "dimmer": {"channel": 1, "range": [0, 255]},
                    "pan": {"channel": 2, "range": [0, 255]},
                    "tilt": {"channel": 3, "range": [0, 255]},
                    "color": {"channel": 4, "range": [0, 255]},
                    "gobo": {"channel": 5, "range": [0, 255]},
                    "beam": {"channel": 6, "range": [0, 255]}
                },
                "color_wheel": ["白", "红", "绿", "蓝", "黄", "紫", "青"],
                "beam_angle": [10, 45],  # 最小和最大光束角
                "pan_range": 540,  # 水平旋转范围
                "tilt_range": 270,  # 垂直旋转范围
                "power": 300  # 功率(W)
            },
            "led_par": {
                "name": "LED帕灯",
                "channels": {
                    "dimmer": {"channel": 1, "range": [0, 255]},
                    "red": {"channel": 2, "range": [0, 255]},
                    "green": {"channel": 3, "range": [0, 255]},
                    "blue": {"channel": 4, "range": [0, 255]},
                    "white": {"channel": 5, "range": [0, 255]},
                    "strobe": {"channel": 6, "range": [0, 255]}
                },
                "color_mixing": "RGBW",
                "beam_angle": [25, 60],
                "power": 150
            },
            "led_strip": {
                "name": "LED灯带",
                "channels": {
                    "dimmer": {"channel": 1, "range": [0, 255]},
                    "red": {"channel": 2, "range": [0, 255]},
                    "green": {"channel": 3, "range": [0, 255]},
                    "blue": {"channel": 4, "range": [0, 255]},
                    "mode": {"channel": 5, "range": [0, 255]}
                },
                "color_mixing": "RGB",
                "length": 5,  # 米
                "power": 72
            },
            "wash_light": {
                "name": "染色灯",
                "channels": {
                    "dimmer": {"channel": 1, "range": [0, 255]},
                    "red": {"channel": 2, "range": [0, 255]},
                    "green": {"channel": 3, "range": [0, 255]},
                    "blue": {"channel": 4, "range": [0, 255]},
                    "white": {"channel": 5, "range": [0, 255]},
                    "amber": {"channel": 6, "range": [0, 255]},
                    "uv": {"channel": 7, "range": [0, 255]}
                },
                "color_mixing": "RGBWAU",
                "beam_angle": [40, 80],
                "power": 200
            }
        }
    
    def import_fixture_library(self, file_path: str) -> bool:
        """导入外部灯具库"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                imported_fixtures = json.load(f)
            
            # 合并灯具库
            self.fixtures.update(imported_fixtures)
            return True
        except Exception as e:
            print(f"导入灯具库失败: {e}")
            return False
    
    def get_fixture_types(self) -> List[str]:
        """获取所有灯具类型"""
        return list(self.fixtures.keys())
    
    def get_fixture_info(self, fixture_type: str) -> Optional[Dict]:
        """获取灯具信息"""
        return self.fixtures.get(fixture_type)

class FixtureManager:
    """灯具配接管理"""
    
    def __init__(self, fixture_library: FixtureLibrary):
        self.fixture_library = fixture_library
        self.configured_fixtures = []  # 已配接的灯具
        self.universe_mapping = {}  # Universe映射
        
    def add_fixture(self, fixture_type: str, start_address: int, universe: int = 0, 
                   zone: str = "默认区域", name: str = "") -> bool:
        """添加灯具配接"""
        fixture_info = self.fixture_library.get_fixture_info(fixture_type)
        if not fixture_info:
            return False
        
        fixture_config = {
            "id": len(self.configured_fixtures) + 1,
            "type": fixture_type,
            "name": name or f"{fixture_info['name']}_{len(self.configured_fixtures) + 1}",
            "start_address": start_address,
            "universe": universe,
            "zone": zone,
            "channels": fixture_info["channels"],
            "properties": {
                "color_mixing": fixture_info.get("color_mixing", "RGB"),
                "beam_angle": fixture_info.get("beam_angle", [30, 60]),
                "power": fixture_info.get("power", 100)
            },
            "current_values": {ch: 0 for ch in fixture_info["channels"].keys()}
        }
        
        self.configured_fixtures.append(fixture_config)
        return True
    
    def get_fixtures_by_zone(self, zone: str) -> List[Dict]:
        """按区域获取灯具"""
        return [f for f in self.configured_fixtures if f["zone"] == zone]
    
    def get_all_zones(self) -> List[str]:
        """获取所有区域"""
        zones = set(f["zone"] for f in self.configured_fixtures)
        return list(zones)

class MusicAnalyzer:
    """音乐分析器（简化版）"""
    
    def __init__(self):
        self.current_analysis = None
    
    def analyze_audio_file(self, file_path: str) -> Dict:
        """分析音频文件"""
        # 这里可以集成librosa等音频处理库
        # 现在使用模拟数据
        
        # 模拟分析过程
        print(f"正在分析音频文件: {file_path}")
        time.sleep(2)  # 模拟处理时间
        
        # 生成模拟分析结果
        bpm = random.uniform(80, 160)
        duration = random.uniform(180, 300)
        
        # 生成节拍时间点
        beat_interval = 60 / bpm
        beat_times = []
        current_time = 0
        while current_time < duration:
            beat_times.append(current_time)
            current_time += beat_interval
        
        # 分析音乐特征
        energy_profile = [random.uniform(0.1, 0.9) for _ in range(int(duration / 10))]  # 每10秒一个能量值
        
        self.current_analysis = {
            "file_path": file_path,
            "bpm": bpm,
            "duration": duration,
            "beat_times": beat_times,
            "energy_profile": energy_profile,
            "style": self._classify_style(bpm, sum(energy_profile) / len(energy_profile)),
            "mood": self._analyze_mood(energy_profile),
            "analysis_time": datetime.now().isoformat()
        }
        
        return self.current_analysis
    
    def _classify_style(self, bpm: float, avg_energy: float) -> str:
        """分类音乐风格"""
        if bpm < 80:
            return "慢板"
        elif bpm < 120:
            return "中板"
        elif bpm < 140:
            return "快板"
        else:
            return "极快"
    
    def _analyze_mood(self, energy_profile: List[float]) -> str:
        """分析音乐情绪"""
        avg_energy = sum(energy_profile) / len(energy_profile)
        energy_variance = sum((e - avg_energy) ** 2 for e in energy_profile) / len(energy_profile)
        
        if avg_energy > 0.7:
            return "激昂"
        elif avg_energy < 0.3:
            return "平静"
        elif energy_variance > 0.1:
            return "动感"
        else:
            return "稳定"

class AILightingEngine:
    """AI灯光引擎"""
    
    def __init__(self, fixture_manager: FixtureManager):
        self.fixture_manager = fixture_manager
        self.lighting_sequence = []
        
    def generate_lighting_sequence(self, music_analysis: Dict) -> List[Dict]:
        """基于音乐分析生成灯光序列"""
        print("🤖 AI正在生成灯光序列...")
        
        beat_times = music_analysis["beat_times"]
        energy_profile = music_analysis["energy_profile"]
        style = music_analysis["style"]
        mood = music_analysis["mood"]
        
        # 根据音乐风格选择基础效果
        base_effects = self._get_base_effects(style, mood)
        
        # 为每个区域生成灯光序列
        zones = self.fixture_manager.get_all_zones()
        self.lighting_sequence = []
        
        for i, beat_time in enumerate(beat_times):
            # 计算当前能量级别
            energy_index = min(int(beat_time / 10), len(energy_profile) - 1)
            current_energy = energy_profile[energy_index]
            
            # 为每个区域生成灯光事件
            for zone in zones:
                fixtures = self.fixture_manager.get_fixtures_by_zone(zone)
                
                for fixture in fixtures:
                    lighting_event = self._generate_fixture_event(
                        fixture, beat_time, current_energy, base_effects, i
                    )
                    self.lighting_sequence.append(lighting_event)
        
        print(f"✅ 生成了 {len(self.lighting_sequence)} 个灯光事件")
        return self.lighting_sequence
    
    def _get_base_effects(self, style: str, mood: str) -> Dict:
        """根据风格和情绪获取基础效果"""
        effects = {
            "慢板": {
                "colors": ["暖白", "琥珀", "淡蓝"],
                "intensity_range": [0.3, 0.7],
                "change_frequency": 4  # 每4拍变化一次
            },
            "中板": {
                "colors": ["白", "红", "蓝", "绿"],
                "intensity_range": [0.5, 0.8],
                "change_frequency": 2
            },
            "快板": {
                "colors": ["红", "白", "蓝", "黄"],
                "intensity_range": [0.7, 1.0],
                "change_frequency": 1
            },
            "极快": {
                "colors": ["彩色循环", "频闪"],
                "intensity_range": [0.8, 1.0],
                "change_frequency": 0.5
            }
        }
        
        return effects.get(style, effects["中板"])
    
    def _generate_fixture_event(self, fixture: Dict, beat_time: float, 
                               energy: float, base_effects: Dict, beat_index: int) -> Dict:
        """为单个灯具生成事件"""
        fixture_type = fixture["type"]
        channels = fixture["channels"]
        
        # 基础强度
        min_intensity, max_intensity = base_effects["intensity_range"]
        intensity = min_intensity + (max_intensity - min_intensity) * energy
        
        # 生成DMX值
        dmx_values = {}
        
        # 调光通道
        if "dimmer" in channels:
            dmx_values["dimmer"] = int(intensity * 255)
        
        # 颜色通道
        if fixture_type == "led_par" or fixture_type == "wash_light":
            # RGB颜色生成
            color_index = beat_index % len(base_effects["colors"])
            color = self._get_rgb_values(base_effects["colors"][color_index], intensity)
            
            if "red" in channels:
                dmx_values["red"] = color[0]
            if "green" in channels:
                dmx_values["green"] = color[1]
            if "blue" in channels:
                dmx_values["blue"] = color[2]
            if "white" in channels:
                dmx_values["white"] = int(intensity * 255 * 0.3)  # 适量白光
        
        # 摇头灯的位置控制
        if fixture_type == "moving_head_spot":
            if "pan" in channels:
                # 根据节拍生成摇摆效果
                pan_value = int(127 + 100 * energy * (1 if beat_index % 2 == 0 else -1))
                dmx_values["pan"] = max(0, min(255, pan_value))
            
            if "tilt" in channels:
                tilt_value = int(127 + 50 * energy * (1 if beat_index % 4 < 2 else -1))
                dmx_values["tilt"] = max(0, min(255, tilt_value))
        
        return {
            "time": beat_time,
            "fixture_id": fixture["id"],
            "fixture_name": fixture["name"],
            "universe": fixture["universe"],
            "start_address": fixture["start_address"],
            "dmx_values": dmx_values,
            "energy_level": energy
        }
    
    def _get_rgb_values(self, color_name: str, intensity: float) -> tuple:
        """获取RGB值"""
        color_map = {
            "红": (255, 0, 0),
            "绿": (0, 255, 0),
            "蓝": (0, 0, 255),
            "白": (255, 255, 255),
            "黄": (255, 255, 0),
            "紫": (255, 0, 255),
            "青": (0, 255, 255),
            "暖白": (255, 200, 150),
            "琥珀": (255, 191, 0),
            "淡蓝": (173, 216, 230)
        }
        
        base_color = color_map.get(color_name, (255, 255, 255))
        return tuple(int(c * intensity) for c in base_color)

class ArtNetController:
    """Art-Net控制器"""
    
    def __init__(self):
        self.socket = None
        self.target_ip = "127.0.0.1"  # Depence默认IP
        self.target_port = 6454
        self.sequence = 0
        self.universe_data = {}  # 存储每个Universe的DMX数据
        
    def setup_connection(self, target_ip: str = "127.0.0.1"):
        """设置Art-Net连接"""
        self.target_ip = target_ip
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            print(f"✅ Art-Net连接已设置，目标: {target_ip}:{self.target_port}")
            return True
        except Exception as e:
            print(f"❌ Art-Net连接设置失败: {e}")
            return False
    
    def send_lighting_event(self, event: Dict):
        """发送单个灯光事件"""
        if not self.socket:
            return False
        
        universe = event["universe"]
        start_address = event["start_address"]
        dmx_values = event["dmx_values"]
        
        # 初始化Universe数据
        if universe not in self.universe_data:
            self.universe_data[universe] = [0] * 512
        
        # 更新DMX数据
        for channel_name, value in dmx_values.items():
            # 这里需要根据灯具的通道映射来设置正确的地址
            # 简化处理：假设通道按顺序排列
            channel_offset = list(dmx_values.keys()).index(channel_name)
            dmx_address = start_address + channel_offset - 1  # DMX地址从1开始
            
            if 0 <= dmx_address < 512:
                self.universe_data[universe][dmx_address] = max(0, min(255, value))
        
        # 发送Art-Net包
        return self._send_artnet_packet(universe, self.universe_data[universe])
    
    def _send_artnet_packet(self, universe: int, dmx_data: List[int]) -> bool:
        """发送Art-Net数据包"""
        try:
            # 构建Art-Net包
            packet = bytearray()
            packet.extend(b"Art-Net\x00")  # Art-Net ID
            packet.extend(struct.pack("<H", 0x5000))  # OpDmx
            packet.extend(struct.pack(">H", 14))  # Protocol version
            packet.append(self.sequence)  # Sequence
            packet.append(0)  # Physical
            packet.append(universe & 0xFF)  # Universe low
            packet.append((universe >> 8) & 0xFF)  # Universe high
            packet.extend(struct.pack(">H", 512))  # Data length
            packet.extend(dmx_data)  # DMX data
            
            # 发送到目标
            self.socket.sendto(packet, (self.target_ip, self.target_port))
            
            self.sequence = (self.sequence + 1) % 256
            return True
            
        except Exception as e:
            print(f"❌ Art-Net发送失败: {e}")
            return False
    
    def close_connection(self):
        """关闭连接"""
        if self.socket:
            self.socket.close()
            self.socket = None

class ProfessionalAILightingGUI:
    """专业AI灯光控制系统界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 专业AI灯光控制系统")
        self.root.geometry("1400x900")
        
        # 初始化组件
        self.fixture_library = FixtureLibrary()
        self.fixture_manager = FixtureManager(self.fixture_library)
        self.music_analyzer = MusicAnalyzer()
        self.ai_engine = AILightingEngine(self.fixture_manager)
        self.artnet_controller = ArtNetController()
        
        # 数据存储
        self.current_music_file = None
        self.current_analysis = None
        self.current_sequence = None
        self.playback_thread = None
        self.is_playing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = tk.Frame(self.root, bg="#1E3A8A", height=70)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🎭 专业AI灯光控制系统", 
                              font=("Arial", 20, "bold"), fg="white", bg="#1E3A8A")
        title_label.pack(expand=True)
        
        subtitle_label = tk.Label(title_frame, text="音乐导入 → 灯具配接 → AI分析 → Art-Net输出 → Depence预览", 
                                 font=("Arial", 12), fg="#93C5FD", bg="#1E3A8A")
        subtitle_label.pack()
        
        # 创建主要选项卡
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 1. 音乐导入选项卡
        self.create_music_import_tab()
        
        # 2. 灯具配接选项卡
        self.create_fixture_config_tab()
        
        # 3. AI分析选项卡
        self.create_ai_analysis_tab()
        
        # 4. 实时控制选项卡
        self.create_realtime_control_tab()
        
        # 状态栏
        self.create_status_bar()
        
    def create_music_import_tab(self):
        """创建音乐导入选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🎵 音乐导入")
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="音频文件选择")
        file_frame.pack(fill="x", padx=10, pady=5)
        
        self.music_file_var = tk.StringVar()
        file_entry = tk.Entry(file_frame, textvariable=self.music_file_var, width=80)
        file_entry.pack(side="left", padx=5, pady=5, fill="x", expand=True)
        
        browse_btn = tk.Button(file_frame, text="📁 浏览", command=self.browse_music_file)
        browse_btn.pack(side="right", padx=5, pady=5)
        
        analyze_btn = tk.Button(file_frame, text="🔍 分析音乐", command=self.analyze_music,
                               bg="#059669", fg="white", font=("Arial", 10, "bold"))
        analyze_btn.pack(side="right", padx=5, pady=5)
        
        # 分析结果显示
        results_frame = ttk.LabelFrame(frame, text="音乐分析结果")
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.music_analysis_text = tk.Text(results_frame, height=20, font=("Consolas", 10))
        music_scroll = ttk.Scrollbar(results_frame, orient="vertical", command=self.music_analysis_text.yview)
        self.music_analysis_text.configure(yscrollcommand=music_scroll.set)
        
        self.music_analysis_text.pack(side="left", fill="both", expand=True)
        music_scroll.pack(side="right", fill="y")
        
    def create_fixture_config_tab(self):
        """创建灯具配接选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="💡 灯具配接")
        
        # 左侧：灯具库
        left_frame = tk.Frame(frame)
        left_frame.pack(side="left", fill="y", padx=5, pady=5)
        
        library_frame = ttk.LabelFrame(left_frame, text="灯具库")
        library_frame.pack(fill="both", expand=True)
        
        # 灯具类型列表
        self.fixture_types_listbox = tk.Listbox(library_frame, height=15)
        for fixture_type in self.fixture_library.get_fixture_types():
            fixture_info = self.fixture_library.get_fixture_info(fixture_type)
            self.fixture_types_listbox.insert("end", fixture_info["name"])
        self.fixture_types_listbox.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 导入灯具库按钮
        import_btn = tk.Button(library_frame, text="📥 导入灯具库", command=self.import_fixture_library)
        import_btn.pack(pady=5)
        
        # 右侧：灯具配接
        right_frame = tk.Frame(frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)
        
        # 配接控制
        config_frame = ttk.LabelFrame(right_frame, text="灯具配接")
        config_frame.pack(fill="x", pady=5)
        
        # 配接参数
        tk.Label(config_frame, text="起始地址:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
        self.start_address_var = tk.StringVar(value="1")
        tk.Entry(config_frame, textvariable=self.start_address_var, width=10).grid(row=0, column=1, padx=5, pady=2)
        
        tk.Label(config_frame, text="Universe:").grid(row=0, column=2, padx=5, pady=2, sticky="w")
        self.universe_var = tk.StringVar(value="0")
        tk.Entry(config_frame, textvariable=self.universe_var, width=10).grid(row=0, column=3, padx=5, pady=2)
        
        tk.Label(config_frame, text="区域:").grid(row=1, column=0, padx=5, pady=2, sticky="w")
        self.zone_var = tk.StringVar(value="舞台前区")
        zone_combo = ttk.Combobox(config_frame, textvariable=self.zone_var, 
                                 values=["舞台前区", "舞台后区", "观众席", "背景区", "特效区"])
        zone_combo.grid(row=1, column=1, padx=5, pady=2)
        
        tk.Label(config_frame, text="名称:").grid(row=1, column=2, padx=5, pady=2, sticky="w")
        self.fixture_name_var = tk.StringVar()
        tk.Entry(config_frame, textvariable=self.fixture_name_var, width=15).grid(row=1, column=3, padx=5, pady=2)
        
        add_fixture_btn = tk.Button(config_frame, text="➕ 添加灯具", command=self.add_fixture,
                                   bg="#DC2626", fg="white", font=("Arial", 10, "bold"))
        add_fixture_btn.grid(row=0, column=4, rowspan=2, padx=10, pady=2)
        
        # 已配接灯具列表
        fixtures_frame = ttk.LabelFrame(right_frame, text="已配接灯具")
        fixtures_frame.pack(fill="both", expand=True, pady=5)
        
        # 创建表格
        columns = ("ID", "名称", "类型", "地址", "Universe", "区域")
        self.fixtures_tree = ttk.Treeview(fixtures_frame, columns=columns, show="headings", height=15)
        
        for col in columns:
            self.fixtures_tree.heading(col, text=col)
            self.fixtures_tree.column(col, width=100)
        
        fixtures_scroll = ttk.Scrollbar(fixtures_frame, orient="vertical", command=self.fixtures_tree.yview)
        self.fixtures_tree.configure(yscrollcommand=fixtures_scroll.set)
        
        self.fixtures_tree.pack(side="left", fill="both", expand=True)
        fixtures_scroll.pack(side="right", fill="y")

    def create_ai_analysis_tab(self):
        """创建AI分析选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🤖 AI分析")

        # AI控制区域
        control_frame = ttk.LabelFrame(frame, text="AI灯光生成")
        control_frame.pack(fill="x", padx=10, pady=5)

        generate_btn = tk.Button(control_frame, text="🤖 生成AI灯光序列",
                                command=self.generate_ai_lighting,
                                bg="#7C3AED", fg="white", font=("Arial", 12, "bold"))
        generate_btn.pack(side="left", padx=10, pady=10)

        export_btn = tk.Button(control_frame, text="💾 导出序列", command=self.export_sequence)
        export_btn.pack(side="left", padx=5, pady=10)

        # Art-Net设置
        artnet_frame = ttk.LabelFrame(control_frame, text="Art-Net设置")
        artnet_frame.pack(side="right", padx=10, pady=5)

        tk.Label(artnet_frame, text="目标IP:").pack(side="left", padx=5)
        self.artnet_ip_var = tk.StringVar(value="127.0.0.1")
        tk.Entry(artnet_frame, textvariable=self.artnet_ip_var, width=15).pack(side="left", padx=5)

        connect_btn = tk.Button(artnet_frame, text="🔗 连接", command=self.connect_artnet)
        connect_btn.pack(side="left", padx=5)

        # 灯光序列显示
        sequence_frame = ttk.LabelFrame(frame, text="AI生成的灯光序列")
        sequence_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建序列表格
        seq_columns = ("时间", "灯具", "区域", "强度", "颜色", "特效")
        self.sequence_tree = ttk.Treeview(sequence_frame, columns=seq_columns, show="headings", height=20)

        for col in seq_columns:
            self.sequence_tree.heading(col, text=col)
            self.sequence_tree.column(col, width=120)

        seq_scroll = ttk.Scrollbar(sequence_frame, orient="vertical", command=self.sequence_tree.yview)
        self.sequence_tree.configure(yscrollcommand=seq_scroll.set)

        self.sequence_tree.pack(side="left", fill="both", expand=True)
        seq_scroll.pack(side="right", fill="y")

    def create_realtime_control_tab(self):
        """创建实时控制选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🎮 实时控制")

        # 播放控制
        playback_frame = ttk.LabelFrame(frame, text="播放控制")
        playback_frame.pack(fill="x", padx=10, pady=5)

        self.play_btn = tk.Button(playback_frame, text="▶️ 播放", command=self.start_playback,
                                 bg="#059669", fg="white", font=("Arial", 12, "bold"))
        self.play_btn.pack(side="left", padx=5, pady=5)

        self.pause_btn = tk.Button(playback_frame, text="⏸️ 暂停", command=self.pause_playback)
        self.pause_btn.pack(side="left", padx=5, pady=5)

        self.stop_btn = tk.Button(playback_frame, text="⏹️ 停止", command=self.stop_playback)
        self.stop_btn.pack(side="left", padx=5, pady=5)

        # 时间显示
        self.time_label = tk.Label(playback_frame, text="00:00 / 00:00", font=("Arial", 14, "bold"))
        self.time_label.pack(side="right", padx=10, pady=5)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_scale = ttk.Scale(playback_frame, from_=0, to=100, orient="horizontal",
                                       variable=self.progress_var, length=400)
        self.progress_scale.pack(side="right", padx=10, pady=5)

        # 实时状态显示
        status_frame = ttk.LabelFrame(frame, text="实时状态")
        status_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 左侧：当前播放信息
        info_frame = tk.Frame(status_frame)
        info_frame.pack(side="left", fill="y", padx=5, pady=5)

        tk.Label(info_frame, text="当前播放信息", font=("Arial", 12, "bold")).pack(anchor="w")

        self.current_time_label = tk.Label(info_frame, text="播放时间: --", font=("Arial", 10))
        self.current_time_label.pack(anchor="w", pady=2)

        self.current_bpm_label = tk.Label(info_frame, text="BPM: --", font=("Arial", 10))
        self.current_bpm_label.pack(anchor="w", pady=2)

        self.current_energy_label = tk.Label(info_frame, text="能量级别: --", font=("Arial", 10))
        self.current_energy_label.pack(anchor="w", pady=2)

        self.artnet_status_label = tk.Label(info_frame, text="Art-Net: 未连接", font=("Arial", 10))
        self.artnet_status_label.pack(anchor="w", pady=2)

        # 右侧：活跃灯具显示
        active_frame = tk.Frame(status_frame)
        active_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)

        tk.Label(active_frame, text="活跃灯具状态", font=("Arial", 12, "bold")).pack(anchor="w")

        # 活跃灯具列表
        active_columns = ("灯具", "强度", "颜色", "状态")
        self.active_tree = ttk.Treeview(active_frame, columns=active_columns, show="headings", height=15)

        for col in active_columns:
            self.active_tree.heading(col, text=col)
            self.active_tree.column(col, width=100)

        active_scroll = ttk.Scrollbar(active_frame, orient="vertical", command=self.active_tree.yview)
        self.active_tree.configure(yscrollcommand=active_scroll.set)

        self.active_tree.pack(side="left", fill="both", expand=True)
        active_scroll.pack(side="right", fill="y")

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, relief="sunken", bd=1, bg="#F3F4F6")
        status_frame.pack(side="bottom", fill="x")

        self.status_label = tk.Label(status_frame, text="就绪 - 请导入音乐文件开始", anchor="w", bg="#F3F4F6")
        self.status_label.pack(side="left", padx=5)

        # 连接状态指示
        self.connection_label = tk.Label(status_frame, text="🔴 Art-Net未连接", anchor="e", bg="#F3F4F6")
        self.connection_label.pack(side="right", padx=5)

    # 功能实现方法
    def browse_music_file(self):
        """浏览音乐文件"""
        file_types = [
            ("音频文件", "*.mp3 *.wav *.flac *.m4a *.aac"),
            ("MP3文件", "*.mp3"),
            ("WAV文件", "*.wav"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=file_types
        )

        if file_path:
            self.music_file_var.set(file_path)
            self.current_music_file = file_path
            self.status_label.config(text=f"已选择音乐文件: {file_path}")

    def analyze_music(self):
        """分析音乐"""
        if not self.current_music_file:
            messagebox.showwarning("警告", "请先选择音乐文件")
            return

        # 在新线程中执行分析
        analysis_thread = threading.Thread(target=self._analyze_music_thread, daemon=True)
        analysis_thread.start()

    def _analyze_music_thread(self):
        """音乐分析线程"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="正在分析音乐..."))

            # 执行分析
            self.current_analysis = self.music_analyzer.analyze_audio_file(self.current_music_file)

            # 更新界面显示
            self.root.after(0, self.update_music_analysis_display)
            self.root.after(0, lambda: self.status_label.config(text="音乐分析完成"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"音乐分析失败: {e}"))

    def update_music_analysis_display(self):
        """更新音乐分析显示"""
        if not self.current_analysis:
            return

        analysis = self.current_analysis

        # 格式化显示文本
        display_text = f"""🎵 音乐分析结果
{'='*60}

文件路径: {analysis['file_path']}
BPM: {analysis['bpm']:.1f}
时长: {analysis['duration']:.1f} 秒
节拍数: {len(analysis['beat_times'])}
音乐风格: {analysis['style']}
情绪: {analysis['mood']}

节拍时间点 (前20个):
"""

        for i, beat_time in enumerate(analysis['beat_times'][:20]):
            display_text += f"  节拍 {i+1:2d}: {beat_time:6.2f}s\n"

        if len(analysis['beat_times']) > 20:
            display_text += f"  ... 还有 {len(analysis['beat_times']) - 20} 个节拍\n"

        display_text += f"\n能量分布 (每10秒):\n"
        for i, energy in enumerate(analysis['energy_profile']):
            display_text += f"  {i*10:3d}s-{(i+1)*10:3d}s: {energy:.2f}\n"

        display_text += f"\n分析完成时间: {analysis['analysis_time']}"

        self.music_analysis_text.delete("1.0", "end")
        self.music_analysis_text.insert("1.0", display_text)

    def import_fixture_library(self):
        """导入灯具库"""
        file_path = filedialog.askopenfilename(
            title="选择灯具库文件",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if self.fixture_library.import_fixture_library(file_path):
                messagebox.showinfo("成功", "灯具库导入成功")
                # 更新灯具类型列表
                self.fixture_types_listbox.delete(0, "end")
                for fixture_type in self.fixture_library.get_fixture_types():
                    fixture_info = self.fixture_library.get_fixture_info(fixture_type)
                    self.fixture_types_listbox.insert("end", fixture_info["name"])
            else:
                messagebox.showerror("错误", "灯具库导入失败")

    def add_fixture(self):
        """添加灯具"""
        # 获取选中的灯具类型
        selection = self.fixture_types_listbox.curselection()
        if not selection:
            messagebox.showwarning("警告", "请先选择灯具类型")
            return

        fixture_types = list(self.fixture_library.get_fixture_types())
        fixture_type = fixture_types[selection[0]]

        try:
            start_address = int(self.start_address_var.get())
            universe = int(self.universe_var.get())
            zone = self.zone_var.get()
            name = self.fixture_name_var.get()

            if self.fixture_manager.add_fixture(fixture_type, start_address, universe, zone, name):
                self.update_fixtures_display()
                self.status_label.config(text=f"已添加灯具: {name or fixture_type}")

                # 自动递增起始地址
                fixture_info = self.fixture_library.get_fixture_info(fixture_type)
                channel_count = len(fixture_info["channels"])
                self.start_address_var.set(str(start_address + channel_count))

            else:
                messagebox.showerror("错误", "添加灯具失败")

        except ValueError:
            messagebox.showerror("错误", "请输入有效的地址和Universe")

    def update_fixtures_display(self):
        """更新灯具显示"""
        # 清除现有数据
        for item in self.fixtures_tree.get_children():
            self.fixtures_tree.delete(item)

        # 添加新数据
        for fixture in self.fixture_manager.configured_fixtures:
            fixture_info = self.fixture_library.get_fixture_info(fixture["type"])
            self.fixtures_tree.insert("", "end", values=(
                fixture["id"],
                fixture["name"],
                fixture_info["name"],
                fixture["start_address"],
                fixture["universe"],
                fixture["zone"]
            ))

    def connect_artnet(self):
        """连接Art-Net"""
        target_ip = self.artnet_ip_var.get()
        if self.artnet_controller.setup_connection(target_ip):
            self.connection_label.config(text=f"🟢 Art-Net已连接: {target_ip}")
            self.artnet_status_label.config(text=f"Art-Net: 已连接 {target_ip}")
            self.status_label.config(text=f"Art-Net连接成功: {target_ip}")
        else:
            self.connection_label.config(text="🔴 Art-Net连接失败")
            self.artnet_status_label.config(text="Art-Net: 连接失败")

    def generate_ai_lighting(self):
        """生成AI灯光序列"""
        if not self.current_analysis:
            messagebox.showwarning("警告", "请先完成音乐分析")
            return

        if not self.fixture_manager.configured_fixtures:
            messagebox.showwarning("警告", "请先配接灯具")
            return

        try:
            self.status_label.config(text="AI正在生成灯光序列...")

            # 生成序列
            self.current_sequence = self.ai_engine.generate_lighting_sequence(self.current_analysis)

            # 更新显示
            self.update_sequence_display()

            self.status_label.config(text=f"AI灯光序列生成完成，共 {len(self.current_sequence)} 个事件")

        except Exception as e:
            messagebox.showerror("错误", f"AI序列生成失败: {e}")

    def update_sequence_display(self):
        """更新序列显示"""
        # 清除现有数据
        for item in self.sequence_tree.get_children():
            self.sequence_tree.delete(item)

        # 添加新数据（只显示前100个事件）
        for event in self.current_sequence[:100]:
            # 格式化显示信息
            time_str = f"{event['time']:.2f}s"
            fixture_name = event['fixture_name']
            zone = next((f['zone'] for f in self.fixture_manager.configured_fixtures
                        if f['id'] == event['fixture_id']), "未知")

            # 计算平均强度
            dmx_values = event['dmx_values']
            avg_intensity = sum(dmx_values.values()) / len(dmx_values) if dmx_values else 0
            intensity_str = f"{avg_intensity:.0f}"

            # 颜色信息
            color_info = "RGB" if any(k in dmx_values for k in ['red', 'green', 'blue']) else "单色"

            # 特效信息
            effect_str = "移动" if any(k in dmx_values for k in ['pan', 'tilt']) else "静态"

            self.sequence_tree.insert("", "end", values=(
                time_str, fixture_name, zone, intensity_str, color_info, effect_str
            ))

        if len(self.current_sequence) > 100:
            self.sequence_tree.insert("", "end", values=(
                "...", f"还有{len(self.current_sequence)-100}个事件", "", "", "", ""
            ))

    def export_sequence(self):
        """导出序列"""
        if not self.current_sequence:
            messagebox.showwarning("警告", "没有可导出的序列")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存灯光序列",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                export_data = {
                    "music_analysis": self.current_analysis,
                    "fixtures": self.fixture_manager.configured_fixtures,
                    "lighting_sequence": self.current_sequence,
                    "export_time": datetime.now().isoformat()
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"序列已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def start_playback(self):
        """开始播放"""
        if not self.current_sequence:
            messagebox.showwarning("警告", "请先生成AI灯光序列")
            return

        if not self.artnet_controller.socket:
            messagebox.showwarning("警告", "请先连接Art-Net")
            return

        if not self.is_playing:
            self.is_playing = True
            self.play_btn.config(text="⏸️ 暂停")
            self.playback_thread = threading.Thread(target=self._playback_thread, daemon=True)
            self.playback_thread.start()
            self.status_label.config(text="开始播放灯光序列...")

    def pause_playback(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.config(text="▶️ 播放")
        self.status_label.config(text="播放已暂停")

    def stop_playback(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.config(text="▶️ 播放")
        self.progress_var.set(0)
        self.time_label.config(text="00:00 / 00:00")
        self.status_label.config(text="播放已停止")

        # 清除所有灯具
        for item in self.active_tree.get_children():
            self.active_tree.delete(item)

    def _playback_thread(self):
        """播放线程"""
        if not self.current_sequence or not self.current_analysis:
            return

        start_time = time.time()
        total_duration = self.current_analysis['duration']

        # 按时间排序事件
        sorted_events = sorted(self.current_sequence, key=lambda x: x['time'])
        event_index = 0

        while self.is_playing and event_index < len(sorted_events):
            current_time = time.time() - start_time

            # 更新进度显示
            progress = (current_time / total_duration) * 100 if total_duration > 0 else 0
            self.root.after(0, lambda: self.progress_var.set(min(progress, 100)))

            # 更新时间显示
            time_str = f"{int(current_time//60):02d}:{int(current_time%60):02d} / {int(total_duration//60):02d}:{int(total_duration%60):02d}"
            self.root.after(0, lambda: self.time_label.config(text=time_str))

            # 处理当前时间的事件
            while (event_index < len(sorted_events) and
                   sorted_events[event_index]['time'] <= current_time):

                event = sorted_events[event_index]

                # 发送Art-Net数据
                self.artnet_controller.send_lighting_event(event)

                # 更新活跃灯具显示
                self.root.after(0, lambda e=event: self.update_active_fixtures(e))

                event_index += 1

            # 检查是否播放完成
            if current_time >= total_duration:
                break

            time.sleep(0.05)  # 50ms更新间隔

        # 播放完成
        self.root.after(0, self.stop_playback)

    def update_active_fixtures(self, event):
        """更新活跃灯具显示"""
        fixture_id = event['fixture_id']
        fixture_name = event['fixture_name']
        dmx_values = event['dmx_values']

        # 计算强度和颜色信息
        avg_intensity = sum(dmx_values.values()) / len(dmx_values) if dmx_values else 0
        intensity_str = f"{avg_intensity:.0f}"

        # 简化的颜色显示
        if 'red' in dmx_values and 'green' in dmx_values and 'blue' in dmx_values:
            color_str = f"RGB({dmx_values['red']},{dmx_values['green']},{dmx_values['blue']})"
        elif 'dimmer' in dmx_values:
            color_str = f"白光({dmx_values['dimmer']})"
        else:
            color_str = "混合"

        status_str = "活跃" if avg_intensity > 10 else "待机"

        # 查找是否已存在该灯具的记录
        existing_item = None
        for item in self.active_tree.get_children():
            if self.active_tree.item(item)['values'][0] == fixture_name:
                existing_item = item
                break

        if existing_item:
            # 更新现有记录
            self.active_tree.item(existing_item, values=(fixture_name, intensity_str, color_str, status_str))
        else:
            # 添加新记录
            self.active_tree.insert("", "end", values=(fixture_name, intensity_str, color_str, status_str))

    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    print("🎭 专业AI灯光控制系统启动中...")

    try:
        app = ProfessionalAILightingGUI()
        print("✅ 系统初始化完成")
        print("💡 请按以下步骤操作:")
        print("   1. 导入音乐文件并分析")
        print("   2. 配接灯具到相应区域")
        print("   3. 生成AI灯光序列")
        print("   4. 连接Art-Net并开始播放")
        print("   5. 在Depence中查看实时效果")
        app.run()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
