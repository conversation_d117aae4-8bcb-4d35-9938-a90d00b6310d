#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实MA XML解析器 - 专门处理真实的MA控台灯具库格式
支持MA2/MA3的复杂XML结构，包括ChannelFunction、Attribute等
"""

import xml.etree.ElementTree as ET
import json
import os
from typing import Dict, List, Optional, Tuple

class RealMAParser:
    """真实MA XML解析器"""
    
    def __init__(self):
        self.fixtures = {}
        self.parse_errors = []
        
    def parse_ma_xml(self, xml_file_path: str) -> bool:
        """解析真实的MA XML文件"""
        try:
            print(f"🔍 开始解析真实MA XML文件: {xml_file_path}")
            
            if not os.path.exists(xml_file_path):
                self.parse_errors.append(f"文件不存在: {xml_file_path}")
                return False
            
            # 读取并解析XML
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            print(f"📄 XML根元素: {root.tag}")
            print(f"📄 XML属性: {root.attrib}")
            
            # 查找所有FixtureType
            fixture_types = root.findall(".//FixtureType")
            print(f"🔍 找到 {len(fixture_types)} 个FixtureType")
            
            fixtures_parsed = 0
            
            for fixture_type in fixture_types:
                fixture_data = self._parse_real_fixture_type(fixture_type)
                if fixture_data:
                    self.fixtures[fixture_data['id']] = fixture_data
                    fixtures_parsed += 1
                    print(f"✅ 解析灯具: {fixture_data['manufacturer']} - {fixture_data['name']}")
            
            print(f"✅ 成功解析 {fixtures_parsed} 个灯具")
            return fixtures_parsed > 0
            
        except ET.ParseError as e:
            error_msg = f"XML解析错误: {e}"
            print(f"❌ {error_msg}")
            self.parse_errors.append(error_msg)
            return False
        except Exception as e:
            error_msg = f"解析过程异常: {e}"
            print(f"❌ {error_msg}")
            self.parse_errors.append(error_msg)
            return False
    
    def _parse_real_fixture_type(self, fixture_element: ET.Element) -> Optional[Dict]:
        """解析真实的FixtureType元素"""
        try:
            # 基本信息
            fixture_name = fixture_element.get('Name', 'Unknown')
            long_name = fixture_element.get('LongName', fixture_name)
            manufacturer = fixture_element.get('Manufacturer', 'Unknown')
            
            print(f"🔍 解析灯具: {manufacturer} - {fixture_name}")
            
            # 解析物理属性
            physical = self._parse_physical_properties(fixture_element)
            
            # 解析所有模式
            modes = self._parse_real_modes(fixture_element)
            
            # 获取主要模式的通道信息
            main_mode = None
            max_channels = 0
            
            for mode_name, mode_data in modes.items():
                if mode_data['channel_count'] > max_channels:
                    max_channels = mode_data['channel_count']
                    main_mode = mode_name
            
            # 从主要模式提取通道信息
            channels = {}
            if main_mode and main_mode in modes:
                channels = modes[main_mode]['channels']
            
            fixture_data = {
                'id': fixture_name,
                'name': long_name,
                'short_name': fixture_name,
                'manufacturer': manufacturer,
                'channels': channels,
                'modes': modes,
                'physical': physical,
                'channel_count': max_channels,
                'source': 'Real_MA_Parser',
                'xml_attributes': dict(fixture_element.attrib)
            }
            
            return fixture_data
            
        except Exception as e:
            print(f"❌ 解析FixtureType失败: {e}")
            return None
    
    def _parse_real_modes(self, fixture_element: ET.Element) -> Dict:
        """解析真实的Mode元素"""
        modes = {}
        
        try:
            mode_elements = fixture_element.findall('.//Mode')
            print(f"🔍 找到 {len(mode_elements)} 个模式")
            
            for mode_element in mode_elements:
                mode_name = mode_element.get('Name', 'Unknown')
                physical_from = int(mode_element.get('PhysicalFrom', 1))
                physical_to = int(mode_element.get('PhysicalTo', 1))
                
                print(f"  📋 解析模式: {mode_name} ({physical_from}-{physical_to})")
                
                # 解析模式中的通道
                channels = self._parse_mode_channels(mode_element)
                
                modes[mode_name] = {
                    'name': mode_name,
                    'physical_from': physical_from,
                    'physical_to': physical_to,
                    'channel_count': physical_to - physical_from + 1,
                    'channels': channels,
                    'xml_attributes': dict(mode_element.attrib)
                }
                
                print(f"    ✅ 模式 {mode_name}: {len(channels)} 个通道定义")
            
        except Exception as e:
            print(f"❌ 解析模式失败: {e}")
        
        return modes
    
    def _parse_mode_channels(self, mode_element: ET.Element) -> Dict:
        """解析模式中的通道"""
        channels = {}
        
        try:
            channel_elements = mode_element.findall('./Channel')
            
            for i, channel_element in enumerate(channel_elements):
                channel_number = i + 1
                
                # 获取通道基本属性
                geometry = channel_element.get('Geometry', 'Unknown')
                feature = channel_element.get('Feature', 'Unknown')
                resolution = channel_element.get('Resolution', '8Bit')
                highlight = channel_element.get('Highlight')
                
                # 解析ChannelFunction
                channel_functions = self._parse_channel_functions(channel_element)
                
                # 确定通道名称和功能
                channel_name = self._determine_channel_name(geometry, feature, channel_functions)
                
                channels[channel_name] = {
                    'channel_number': channel_number,
                    'geometry': geometry,
                    'feature': feature,
                    'resolution': resolution,
                    'highlight': highlight,
                    'functions': channel_functions,
                    'xml_attributes': dict(channel_element.attrib)
                }
                
        except Exception as e:
            print(f"❌ 解析通道失败: {e}")
        
        return channels
    
    def _parse_channel_functions(self, channel_element: ET.Element) -> List[Dict]:
        """解析ChannelFunction元素"""
        functions = []
        
        try:
            function_elements = channel_element.findall('./ChannelFunction')
            
            for func_element in function_elements:
                function_data = {
                    'attribute': func_element.get('Attribute', ''),
                    'original_attribute': func_element.get('OriginalAttribute', ''),
                    'dmx_from': int(func_element.get('DMXFrom', 0)),
                    'dmx_to': int(func_element.get('DMXTo', 255)),
                    'physical_from': func_element.get('PhysicalFrom', '0'),
                    'physical_to': func_element.get('PhysicalTo', '1'),
                    'real_fade': func_element.get('RealFade'),
                    'real_acceleration': func_element.get('RealAcceleration'),
                    'color': func_element.get('Color'),
                    'xml_attributes': dict(func_element.attrib)
                }
                
                functions.append(function_data)
                
        except Exception as e:
            print(f"❌ 解析ChannelFunction失败: {e}")
        
        return functions
    
    def _determine_channel_name(self, geometry: str, feature: str, functions: List[Dict]) -> str:
        """确定通道名称"""
        # 优先使用第一个function的attribute
        if functions and functions[0]['attribute']:
            return functions[0]['attribute'].lower()
        
        # 使用feature
        if feature and feature != 'Unknown':
            return feature.lower().replace('_n_', '_').replace('_', '_')
        
        # 使用geometry
        if geometry and geometry != 'Unknown':
            return geometry.lower()
        
        return 'unknown'
    
    def _parse_physical_properties(self, fixture_element: ET.Element) -> Dict:
        """解析物理属性"""
        physical = {}
        
        try:
            phys_element = fixture_element.find('./Physical')
            if phys_element is not None:
                # 解析所有物理属性
                for attr_name, attr_value in phys_element.attrib.items():
                    # 尝试转换为数值
                    try:
                        # 移除单位后缀
                        clean_value = attr_value.replace('W', '').replace('kg', '').replace('°', '')
                        
                        if '.' in clean_value:
                            physical[attr_name.lower()] = float(clean_value)
                        elif clean_value.isdigit():
                            physical[attr_name.lower()] = int(clean_value)
                        else:
                            physical[attr_name.lower()] = attr_value
                    except:
                        physical[attr_name.lower()] = attr_value
                        
        except Exception as e:
            print(f"❌ 解析物理属性失败: {e}")
        
        return physical
    
    def get_fixture_summary(self, fixture_id: str) -> Optional[str]:
        """获取灯具摘要信息"""
        if fixture_id not in self.fixtures:
            return None
        
        fixture = self.fixtures[fixture_id]
        
        summary = f"""
🎭 {fixture['manufacturer']} - {fixture['name']}
{'='*50}
短名称: {fixture['short_name']}
制造商: {fixture['manufacturer']}
最大通道数: {fixture['channel_count']}

📋 可用模式:
"""
        
        for mode_name, mode_data in fixture['modes'].items():
            summary += f"  • {mode_name}: {mode_data['channel_count']}通道\n"
        
        summary += f"\n🔧 物理属性:\n"
        for prop_name, prop_value in fixture['physical'].items():
            summary += f"  • {prop_name}: {prop_value}\n"
        
        summary += f"\n📡 主要通道:\n"
        for ch_name, ch_data in list(fixture['channels'].items())[:10]:
            summary += f"  • {ch_data['channel_number']:2d}. {ch_name} ({ch_data['feature']})\n"
        
        if len(fixture['channels']) > 10:
            summary += f"  ... 还有 {len(fixture['channels']) - 10} 个通道\n"
        
        return summary
    
    def export_to_json(self, output_file: str) -> bool:
        """导出为JSON格式"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.fixtures, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 已导出到JSON文件: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出JSON失败: {e}")
            return False
    
    def get_statistics(self) -> Dict:
        """获取解析统计"""
        manufacturers = set(fixture['manufacturer'] for fixture in self.fixtures.values())
        
        channel_counts = {}
        for fixture in self.fixtures.values():
            count = fixture['channel_count']
            channel_counts[count] = channel_counts.get(count, 0) + 1
        
        return {
            'total_fixtures': len(self.fixtures),
            'manufacturers': list(manufacturers),
            'manufacturer_count': len(manufacturers),
            'channel_distribution': channel_counts,
            'parse_errors': self.parse_errors,
            'error_count': len(self.parse_errors)
        }
    
    def list_all_fixtures(self) -> List[str]:
        """列出所有灯具ID"""
        return list(self.fixtures.keys())
    
    def get_fixture_info(self, fixture_id: str) -> Optional[Dict]:
        """获取灯具信息"""
        return self.fixtures.get(fixture_id)

def test_real_ma_parser():
    """测试真实MA解析器"""
    print("🧪 测试真实MA XML解析器")
    
    parser = RealMAParser()
    
    # 测试真实的MA文件
    test_files = [
        'acme@<EMAIL>',
        'sample_ma_fixtures.xml'
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n🔍 测试文件: {test_file}")
            
            if parser.parse_ma_xml(test_file):
                stats = parser.get_statistics()
                print(f"\n📊 解析统计:")
                print(f"  总灯具数: {stats['total_fixtures']}")
                print(f"  制造商数: {stats['manufacturer_count']}")
                print(f"  制造商: {', '.join(stats['manufacturers'])}")
                print(f"  通道分布: {stats['channel_distribution']}")
                
                if stats['parse_errors']:
                    print(f"\n⚠️ 解析错误:")
                    for error in stats['parse_errors']:
                        print(f"  - {error}")
                
                # 显示第一个灯具的详细信息
                if parser.fixtures:
                    first_fixture_id = list(parser.fixtures.keys())[0]
                    summary = parser.get_fixture_summary(first_fixture_id)
                    print(f"\n📋 灯具详情示例:")
                    print(summary)
                
                # 导出JSON
                output_file = f"real_ma_{test_file.replace('.xml', '.json')}"
                if parser.export_to_json(output_file):
                    print(f"\n💾 已导出到: {output_file}")
                
            else:
                print(f"❌ 解析失败: {test_file}")
        else:
            print(f"⚠️ 文件不存在: {test_file}")
    
    return len(parser.fixtures) > 0

if __name__ == "__main__":
    test_real_ma_parser()
