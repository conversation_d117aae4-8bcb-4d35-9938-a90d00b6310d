# 🎭 高级功能完整测试指南

## 📋 测试概述

本指南将帮助您全面测试多媒体演出控制中心的所有高级功能，确保系统正常运行并发挥最大效用。

## 🚀 测试前准备

### 1. 系统启动
1. 运行 `ma2_msc_commander.py`
2. 等待系统完全加载
3. 使用管理员账户登录（用户名：admin，密码：admin123）

### 2. 检查系统状态
- 确认所有标签页正常显示
- 检查控制台输出无严重错误
- 验证网络连接正常

## 🎭 专业灯光协议测试

### 测试1: Art-Net协议功能

#### 基本配置测试
1. 进入 **"高级功能"** 标签页
2. 找到 **"📡 Art-Net 协议"** 区域
3. 配置参数：
   - Universe: 1
   - Subnet: 0
   - Net: 0
   - 广播IP: ***************
4. 点击 **"启用"** 开关

#### 功能测试
1. 点击 **"🧪 测试Art-Net"** 按钮
2. 观察测试结果对话框
3. 检查控制台输出的详细信息
4. 点击 **"📊 监控数据"** 查看统计

#### 网络诊断测试
1. 点击 **"🔍 网络诊断"** 按钮
2. 等待诊断完成
3. 查看诊断结果：
   - 网络接口信息
   - Art-Net端口检查
   - 广播功能测试
   - 防火墙检查
   - 网络连通性
4. 测试 **"📋 复制结果"** 和 **"💾 保存报告"** 功能

### 测试2: sACN协议功能

#### 基本配置测试
1. 找到 **"🌐 sACN (E1.31) 协议"** 区域
2. 配置参数：
   - Universe: 1
   - Priority: 100
   - Source Name: 多媒体演出控制中心
3. 点击 **"启用"** 开关

#### 功能测试
1. 点击 **"🧪 测试sACN"** 按钮
2. 验证组播地址计算正确
3. 检查测试结果详细信息
4. 查看监控数据

### 测试3: OSC协议功能

#### 配置测试
1. 找到 **"🎛️ OSC 协议"** 区域
2. 配置参数：
   - 监听端口: 8000
   - 目标IP: 127.0.0.1
   - 目标端口: 9000
3. 启用OSC协议

#### 功能测试
1. 点击 **"🧪 测试OSC"** 按钮
2. 验证OSC消息格式
3. 检查发送状态

### 测试4: MIDI Show Control功能

#### 配置测试
1. 找到 **"🎵 MIDI Show Control 协议"** 区域
2. 配置参数：
   - Device ID: 0
   - Command Format: All Call (127)
3. 启用MSC协议

#### 功能测试
1. 点击 **"🧪 测试MSC"** 按钮
2. 验证MSC命令发送
3. 检查设备响应

## 💡 灯具库管理测试

### 测试5: 灯具库浏览功能

#### 基本浏览测试
1. 点击 **"📚 灯具库"** 按钮
2. 查看预置灯具列表
3. 测试分类筛选功能
4. 测试搜索功能

#### 搜索筛选测试
1. 在分类下拉菜单中选择 "Moving Head"
2. 验证筛选结果正确
3. 在搜索框输入 "LED"
4. 验证搜索结果准确
5. 清空搜索条件，确认显示所有灯具

### 测试6: 添加自定义灯具

#### 创建新灯具测试
1. 点击 **"➕ 添加灯具"** 按钮
2. 填写灯具信息：
   - 灯具名称: 测试灯具
   - 制造商: 测试厂商
   - 分类: LED Par
   - 通道数: 7
   - 描述: 测试用LED帕灯

#### DMX模式配置测试
1. 点击 **"➕ 添加模式"** 按钮
2. 创建第一个模式：
   - 模式名称: 7ch
   - 通道数: 7
   - 描述: 全功能模式
3. 创建第二个模式：
   - 模式名称: 3ch
   - 通道数: 3
   - 描述: 简化模式

#### 协议支持测试
1. 选择支持的协议：
   - ✅ Art-Net
   - ✅ sACN
   - ❌ OSC
   - ❌ MSC
2. 点击 **"🧪 测试协议"** 按钮
3. 验证协议测试结果

#### 保存测试
1. 点击 **"💾 保存"** 按钮
2. 确认灯具已添加到列表
3. 验证灯具信息显示正确

### 测试7: 编辑现有灯具

#### 编辑功能测试
1. 在灯具列表中找到刚创建的测试灯具
2. 点击 **"✏️"** 编辑按钮
3. 修改灯具信息：
   - 更改描述为: 已编辑的测试灯具
   - 添加新的DMX模式
4. 保存更改
5. 验证修改已生效

### 测试8: 导入导出功能

#### 导出测试
1. 点击 **"📤 导出灯具"** 按钮
2. 选择保存位置
3. 确认文件成功保存
4. 检查导出的JSON文件内容

#### 导入测试
1. 点击 **"📥 导入灯具"** 按钮
2. 选择刚才导出的文件
3. 确认导入成功
4. 验证没有重复灯具

### 测试9: 删除功能

#### 删除单个灯具测试
1. 选择要删除的测试灯具
2. 点击 **"🗑️"** 删除按钮
3. 确认删除操作
4. 验证灯具已从列表中移除

## 📊 协议监控测试

### 测试10: 实时状态监控

#### 状态显示测试
1. 启用多个协议
2. 发送测试数据包
3. 观察状态标签的实时更新
4. 验证发送计数正确

#### 监控窗口测试
1. 打开各协议的监控窗口
2. 测试 **"🔄 刷新"** 功能
3. 测试 **"🗑️ 清除统计"** 功能
4. 验证统计数据准确性

## 🔧 故障排除测试

### 测试11: 错误处理

#### 网络错误测试
1. 配置错误的IP地址
2. 尝试发送测试数据包
3. 验证错误信息显示正确
4. 检查错误处理机制

#### 参数验证测试
1. 输入超出范围的Universe值
2. 输入无效的端口号
3. 验证参数验证功能
4. 确认错误提示准确

### 测试12: 性能测试

#### 批量操作测试
1. 连续发送多个测试数据包
2. 观察系统响应时间
3. 检查内存使用情况
4. 验证系统稳定性

#### 并发测试
1. 同时启用所有协议
2. 并发发送测试数据
3. 检查协议间是否互相干扰
4. 验证数据包发送正确

## ✅ 测试完成检查清单

### 基础功能 ✅
- [ ] Art-Net协议配置和测试正常
- [ ] sACN协议配置和测试正常
- [ ] OSC协议配置和测试正常
- [ ] MSC协议配置和测试正常
- [ ] 协议状态监控正常显示

### 灯具库功能 ✅
- [ ] 灯具库浏览功能正常
- [ ] 搜索筛选功能正常
- [ ] 添加自定义灯具功能正常
- [ ] 编辑灯具功能正常
- [ ] 删除灯具功能正常
- [ ] 导入导出功能正常

### 高级功能 ✅
- [ ] 网络诊断功能正常
- [ ] 协议监控功能正常
- [ ] 错误处理机制正常
- [ ] 性能表现良好

### 用户体验 ✅
- [ ] 界面响应及时
- [ ] 操作逻辑直观
- [ ] 错误提示友好
- [ ] 功能说明清晰

## 🎯 测试成功标准

### 功能完整性
- 所有协议功能正常工作
- 灯具库管理功能完整
- 网络诊断准确有效
- 监控数据实时更新

### 稳定性
- 长时间运行无崩溃
- 内存使用稳定
- 网络连接可靠
- 错误恢复正常

### 性能
- 响应时间 < 1秒
- 数据包发送成功率 > 95%
- 界面操作流畅
- 资源占用合理

### 易用性
- 功能布局合理
- 操作步骤简单
- 提示信息清晰
- 学习成本低

## 📞 技术支持

测试过程中如遇问题，请联系：
- **开发者**: 徐小龙
- **电话**: 15692899229
- **邮箱**: <EMAIL>

**版本**: V16 终极版
**测试日期**: 2025-06-18
