#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的MA XML解析器 - 修复版本
支持多种MA控台格式，增强错误处理
"""

import xml.etree.ElementTree as ET
import json
import os
from typing import Dict, List, Optional

class EnhancedMAParser:
    """增强的MA XML解析器"""
    
    def __init__(self):
        self.fixtures = {}
        self.parse_errors = []
        
    def parse_xml_file(self, xml_file_path: str) -> bool:
        """解析XML文件"""
        try:
            print(f"🔍 开始解析XML文件: {xml_file_path}")
            
            if not os.path.exists(xml_file_path):
                self.parse_errors.append(f"文件不存在: {xml_file_path}")
                return False
            
            # 读取并解析XML
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            print(f"📄 XML根元素: {root.tag}")
            
            # 尝试不同的解析策略
            success = False
            
            # 策略1: MA2/MA3标准格式
            if root.tag == "MA":
                success = self._parse_ma_standard_format(root)
            
            # 策略2: 通用灯具库格式
            elif root.tag in ["FixtureLibrary", "Fixtures"]:
                success = self._parse_generic_format(root)
            
            # 策略3: 其他可能的格式
            else:
                success = self._parse_unknown_format(root)
            
            if success:
                print(f"✅ 成功解析 {len(self.fixtures)} 个灯具")
                return True
            else:
                print("❌ 所有解析策略都失败了")
                return False
                
        except ET.ParseError as e:
            error_msg = f"XML解析错误: {e}"
            print(f"❌ {error_msg}")
            self.parse_errors.append(error_msg)
            return False
        except Exception as e:
            error_msg = f"解析过程异常: {e}"
            print(f"❌ {error_msg}")
            self.parse_errors.append(error_msg)
            return False
    
    def _parse_ma_standard_format(self, root: ET.Element) -> bool:
        """解析MA标准格式"""
        try:
            fixtures_found = 0
            
            # 查找所有可能的灯具定义路径
            fixture_paths = [
                ".//FixtureType",           # MA2格式
                ".//Info/FixtureType",      # MA2嵌套格式
                ".//FixtureTypes/FixtureType",  # MA3格式
                ".//Fixture",               # 通用格式
                ".//FixtureDefinition"      # 其他可能格式
            ]
            
            for path in fixture_paths:
                fixtures = root.findall(path)
                print(f"🔍 路径 '{path}' 找到 {len(fixtures)} 个灯具")
                
                for fixture_element in fixtures:
                    fixture_data = self._parse_fixture_element(fixture_element)
                    if fixture_data:
                        self.fixtures[fixture_data['id']] = fixture_data
                        fixtures_found += 1
            
            return fixtures_found > 0
            
        except Exception as e:
            print(f"❌ MA标准格式解析失败: {e}")
            return False
    
    def _parse_generic_format(self, root: ET.Element) -> bool:
        """解析通用格式"""
        try:
            fixtures_found = 0
            
            for fixture_element in root.findall(".//Fixture"):
                fixture_data = self._parse_generic_fixture(fixture_element)
                if fixture_data:
                    self.fixtures[fixture_data['id']] = fixture_data
                    fixtures_found += 1
            
            return fixtures_found > 0
            
        except Exception as e:
            print(f"❌ 通用格式解析失败: {e}")
            return False
    
    def _parse_unknown_format(self, root: ET.Element) -> bool:
        """解析未知格式"""
        try:
            print(f"🔍 尝试解析未知格式，根元素: {root.tag}")
            
            # 递归查找所有可能包含灯具信息的元素
            potential_fixtures = []
            
            def find_fixture_elements(element, depth=0):
                if depth > 10:  # 防止无限递归
                    return
                
                # 检查元素是否可能是灯具定义
                if self._is_potential_fixture(element):
                    potential_fixtures.append(element)
                
                # 递归检查子元素
                for child in element:
                    find_fixture_elements(child, depth + 1)
            
            find_fixture_elements(root)
            
            print(f"🔍 找到 {len(potential_fixtures)} 个潜在灯具元素")
            
            fixtures_found = 0
            for element in potential_fixtures:
                fixture_data = self._parse_fixture_element(element)
                if fixture_data:
                    self.fixtures[fixture_data['id']] = fixture_data
                    fixtures_found += 1
            
            return fixtures_found > 0
            
        except Exception as e:
            print(f"❌ 未知格式解析失败: {e}")
            return False
    
    def _is_potential_fixture(self, element: ET.Element) -> bool:
        """判断元素是否可能是灯具定义"""
        # 检查元素名称
        fixture_tags = ['FixtureType', 'Fixture', 'FixtureDefinition', 'Device', 'Light']
        if element.tag in fixture_tags:
            return True
        
        # 检查属性
        fixture_attrs = ['Name', 'name', 'Type', 'type', 'Model', 'model']
        if any(attr in element.attrib for attr in fixture_attrs):
            return True
        
        # 检查是否包含通道信息
        if element.find('.//Channel') is not None or element.find('.//channel') is not None:
            return True
        
        return False
    
    def _parse_fixture_element(self, element: ET.Element) -> Optional[Dict]:
        """解析单个灯具元素"""
        try:
            # 获取基本信息
            fixture_id = (element.get('Name') or element.get('name') or 
                         element.get('ID') or element.get('id') or 
                         f"fixture_{len(self.fixtures) + 1}")
            
            manufacturer = (element.get('Manufacturer') or element.get('manufacturer') or 
                          element.get('Brand') or element.get('brand') or '未知制造商')
            
            fixture_name = (element.get('LongName') or element.get('longname') or 
                          element.get('DisplayName') or element.get('displayname') or 
                          element.get('Name') or element.get('name') or fixture_id)
            
            # 解析通道
            channels = self._parse_channels_flexible(element)
            
            # 解析模式
            modes = self._parse_modes_flexible(element)
            
            # 解析物理属性
            physical = self._parse_physical_flexible(element)
            
            # 解析颜色轮和图案轮
            color_wheels = self._parse_wheels_flexible(element, 'Color')
            gobo_wheels = self._parse_wheels_flexible(element, 'Gobo')
            
            fixture_data = {
                'id': fixture_id,
                'name': fixture_name,
                'manufacturer': manufacturer,
                'channels': channels,
                'modes': modes,
                'physical': physical,
                'color_wheels': color_wheels,
                'gobo_wheels': gobo_wheels,
                'channel_count': len(channels),
                'source': 'Enhanced_Parser'
            }
            
            print(f"✅ 解析灯具: {manufacturer} - {fixture_name} ({len(channels)}通道)")
            return fixture_data
            
        except Exception as e:
            print(f"❌ 解析灯具元素失败: {e}")
            return None
    
    def _parse_generic_fixture(self, element: ET.Element) -> Optional[Dict]:
        """解析通用格式灯具"""
        try:
            fixture_id = element.get('id') or element.get('name') or f"generic_{len(self.fixtures) + 1}"
            manufacturer = element.get('manufacturer') or element.get('brand') or '通用'
            fixture_name = element.get('name') or element.get('model') or fixture_id
            
            # 简化的通道解析
            channels = {}
            channel_elements = element.findall('.//Channel') or element.findall('.//channel')
            
            for i, ch in enumerate(channel_elements):
                ch_name = ch.get('name') or ch.get('function') or f"channel_{i+1}"
                channels[ch_name.lower()] = {
                    'channel': i + 1,
                    'function': ch.get('function') or ch.get('type') or 'Dimmer',
                    'range': [0, 255],
                    'default': int(ch.get('default') or 0)
                }
            
            return {
                'id': fixture_id,
                'name': fixture_name,
                'manufacturer': manufacturer,
                'channels': channels,
                'modes': {'default': {'channels': list(channels.keys())}},
                'physical': {},
                'color_wheels': [],
                'gobo_wheels': [],
                'channel_count': len(channels),
                'source': 'Generic_Parser'
            }
            
        except Exception as e:
            print(f"❌ 解析通用灯具失败: {e}")
            return None
    
    def _parse_channels_flexible(self, element: ET.Element) -> Dict:
        """灵活解析通道信息"""
        channels = {}
        
        try:
            # 尝试多种通道查找方式
            channel_paths = ['.//Channel', './/channel', './/Attribute', './/attribute']
            
            for path in channel_paths:
                channel_elements = element.findall(path)
                if channel_elements:
                    for ch in channel_elements:
                        ch_info = self._parse_single_channel_flexible(ch)
                        if ch_info:
                            channels[ch_info['name']] = ch_info
                    break  # 找到通道就停止尝试其他路径
            
            # 如果还是没找到，尝试从模式中提取
            if not channels:
                mode_elements = element.findall('.//Mode') or element.findall('.//mode')
                for mode in mode_elements:
                    mode_channels = mode.findall('.//Channel') or mode.findall('.//channel')
                    for ch in mode_channels:
                        ch_info = self._parse_single_channel_flexible(ch)
                        if ch_info:
                            channels[ch_info['name']] = ch_info
                    if channels:  # 找到就停止
                        break
            
        except Exception as e:
            print(f"❌ 通道解析失败: {e}")
        
        return channels
    
    def _parse_single_channel_flexible(self, element: ET.Element) -> Optional[Dict]:
        """灵活解析单个通道"""
        try:
            # 尝试多种名称获取方式
            name = (element.get('Name') or element.get('name') or 
                   element.get('Function') or element.get('function') or 
                   element.text or 'unknown').lower().strip()
            
            if not name or name == 'unknown':
                return None
            
            function = (element.get('Function') or element.get('function') or 
                       element.get('Type') or element.get('type') or 'Dimmer')
            
            # 解析范围
            min_val = int(element.get('Min') or element.get('min') or 0)
            max_val = int(element.get('Max') or element.get('max') or 255)
            
            # 解析默认值
            default_val = int(element.get('Default') or element.get('default') or 0)
            
            return {
                'name': name,
                'function': function,
                'range': [min_val, max_val],
                'default': default_val,
                'resolution': element.get('Resolution') or element.get('resolution') or '8bit'
            }
            
        except Exception as e:
            print(f"❌ 单个通道解析失败: {e}")
            return None
    
    def _parse_modes_flexible(self, element: ET.Element) -> Dict:
        """灵活解析模式信息"""
        modes = {}
        
        try:
            mode_elements = element.findall('.//Mode') or element.findall('.//mode')
            
            for mode in mode_elements:
                mode_name = mode.get('Name') or mode.get('name') or f"Mode_{len(modes)+1}"
                
                # 获取模式中的通道
                mode_channels = []
                channel_refs = mode.findall('.//Channel') or mode.findall('.//channel')
                
                for ch_ref in channel_refs:
                    ch_name = (ch_ref.get('Name') or ch_ref.get('name') or 
                              ch_ref.get('Function') or ch_ref.get('function'))
                    if ch_name:
                        mode_channels.append(ch_name.lower())
                
                if mode_channels:
                    modes[mode_name] = {
                        'channels': mode_channels,
                        'channel_count': len(mode_channels)
                    }
            
            # 如果没有找到模式，创建默认模式
            if not modes:
                modes['default'] = {
                    'channels': ['dimmer', 'red', 'green', 'blue'],
                    'channel_count': 4
                }
                
        except Exception as e:
            print(f"❌ 模式解析失败: {e}")
        
        return modes
    
    def _parse_physical_flexible(self, element: ET.Element) -> Dict:
        """灵活解析物理属性"""
        physical = {}
        
        try:
            phys_element = element.find('.//Physical') or element.find('.//physical')
            
            if phys_element is not None:
                # 解析各种物理属性
                attrs = ['Power', 'power', 'Weight', 'weight', 'Width', 'width', 
                        'Height', 'height', 'Depth', 'depth', 'BeamAngle', 'beamangle',
                        'PanRange', 'panrange', 'TiltRange', 'tiltrange']
                
                for attr in attrs:
                    value = phys_element.get(attr)
                    if value:
                        # 清理数值
                        clean_value = ''.join(filter(str.isdigit, value.replace('.', 'X')))
                        clean_value = clean_value.replace('X', '.')
                        try:
                            physical[attr.lower()] = float(clean_value) if '.' in clean_value else int(clean_value)
                        except:
                            physical[attr.lower()] = value
                            
        except Exception as e:
            print(f"❌ 物理属性解析失败: {e}")
        
        return physical
    
    def _parse_wheels_flexible(self, element: ET.Element, wheel_type: str) -> List[Dict]:
        """灵活解析轮子信息"""
        wheels = []
        
        try:
            wheel_elements = element.findall(f'.//{wheel_type}Wheel') or element.findall(f'.//{wheel_type.lower()}wheel')
            
            for wheel in wheel_elements:
                wheel_data = {
                    'name': wheel.get('Name') or wheel.get('name') or f'{wheel_type} Wheel',
                    'items': []
                }
                
                item_elements = wheel.findall(f'.//{wheel_type}') or wheel.findall(f'.//{wheel_type.lower()}')
                
                for item in item_elements:
                    item_name = item.get('Name') or item.get('name') or item.text
                    item_value = item.get('Value') or item.get('value') or '0'
                    
                    if item_name:
                        item_data = {
                            'name': item_name,
                            'value': int(item_value) if item_value.isdigit() else 0
                        }
                        
                        # 如果是颜色轮，添加RGB信息
                        if wheel_type == 'Color':
                            item_data['rgb'] = item.get('RGB') or item.get('rgb') or '#FFFFFF'
                        
                        wheel_data['items'].append(item_data)
                
                if wheel_data['items']:
                    wheels.append(wheel_data)
                    
        except Exception as e:
            print(f"❌ {wheel_type}轮解析失败: {e}")
        
        return wheels
    
    def get_statistics(self) -> Dict:
        """获取解析统计"""
        manufacturers = set(fixture['manufacturer'] for fixture in self.fixtures.values())
        
        return {
            'total_fixtures': len(self.fixtures),
            'manufacturers': list(manufacturers),
            'manufacturer_count': len(manufacturers),
            'parse_errors': self.parse_errors,
            'error_count': len(self.parse_errors)
        }
    
    def export_to_json(self, output_file: str) -> bool:
        """导出为JSON"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.fixtures, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ JSON导出失败: {e}")
            return False

def test_enhanced_parser():
    """测试增强解析器"""
    print("🧪 测试增强MA XML解析器")
    
    parser = EnhancedMAParser()
    
    # 测试示例文件
    if parser.parse_xml_file('sample_ma_fixtures.xml'):
        stats = parser.get_statistics()
        print(f"\n📊 解析统计:")
        print(f"  总灯具数: {stats['total_fixtures']}")
        print(f"  制造商数: {stats['manufacturer_count']}")
        print(f"  错误数: {stats['error_count']}")
        
        if stats['parse_errors']:
            print(f"\n⚠️ 解析错误:")
            for error in stats['parse_errors']:
                print(f"  - {error}")
        
        # 显示解析的灯具
        print(f"\n🔍 解析的灯具:")
        for fixture_id, fixture in parser.fixtures.items():
            print(f"  {fixture['manufacturer']} - {fixture['name']} ({fixture['channel_count']}通道)")
        
        # 导出JSON
        if parser.export_to_json('enhanced_ma_fixtures.json'):
            print(f"\n💾 已导出到: enhanced_ma_fixtures.json")
        
        return True
    else:
        print("❌ 解析失败")
        stats = parser.get_statistics()
        if stats['parse_errors']:
            print(f"\n⚠️ 错误详情:")
            for error in stats['parse_errors']:
                print(f"  - {error}")
        return False

if __name__ == "__main__":
    test_enhanced_parser()
