#!/usr/bin/env python3
"""
Art-Net测试脚本
用于验证与MA2等专业控台的兼容性
"""

import socket
import struct
import time
import sys

def create_artnet_packet(universe, subnet, net, dmx_data):
    """创建标准Art-Net数据包"""
    try:
        # 确保DMX数据长度为512
        if len(dmx_data) < 512:
            dmx_data = list(dmx_data) + [0] * (512 - len(dmx_data))
        elif len(dmx_data) > 512:
            dmx_data = dmx_data[:512]

        # Art-Net头部 - 严格按照Art-Net 4标准
        header = b"Art-Net\x00"  # 8字节标识符
        opcode = struct.pack("<H", 0x5000)  # OpDmx (小端序)
        protocol_version = struct.pack(">H", 14)  # 协议版本14 (大端序)
        sequence = struct.pack("B", 0)  # 序列号 (0表示不使用序列)
        physical = struct.pack("B", 0)  # 物理输入端口

        # Universe地址计算 - 标准Art-Net格式
        universe_low = universe & 0xFF
        universe_high = ((net & 0x7F) << 1) | ((subnet & 0x0F) << 4) | ((universe >> 8) & 0x0F)
        universe_bytes = struct.pack("BB", universe_low, universe_high)

        # 数据长度 - 必须是偶数，最小2，最大512
        data_length = len(dmx_data)
        if data_length % 2 != 0:
            data_length += 1
            dmx_data.append(0)
        
        length = struct.pack(">H", data_length)  # 大端序

        # 组装完整的Art-Net数据包
        packet = (header + opcode + protocol_version + sequence + 
                 physical + universe_bytes + length + bytes(dmx_data))

        return packet

    except Exception as e:
        print(f"❌ 创建Art-Net数据包失败: {e}")
        return None

def send_artnet_packet(packet_data, target_ip, port=6454):
    """发送Art-Net数据包"""
    try:
        # 创建UDP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        
        # 发送数据包
        sock.sendto(packet_data, (target_ip, port))
        sock.close()
        
        print(f"✅ Art-Net数据包已发送到 {target_ip}:{port}")
        print(f"   数据包大小: {len(packet_data)} 字节")
        return True
        
    except Exception as e:
        print(f"❌ Art-Net数据包发送失败: {e}")
        return False

def test_artnet_patterns():
    """测试各种Art-Net模式"""
    print("🎛️ Art-Net专业测试工具")
    print("=" * 50)
    
    universe = 1
    subnet = 0
    net = 0
    target_ip = "***************"
    
    print(f"配置: Universe={universe}, Subnet={subnet}, Net={net}")
    print(f"目标: {target_ip}:6454")
    print()
    
    # 测试模式1: 跑马灯
    print("🏃 测试模式1: 跑马灯效果")
    dmx_data = [0] * 512
    for i in range(0, 512, 10):
        dmx_data[i] = 255
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        send_artnet_packet(packet, target_ip)
    
    time.sleep(2)
    
    # 测试模式2: 渐变
    print("🌈 测试模式2: 渐变效果")
    dmx_data = [0] * 512
    for i in range(256):
        if i < 512:
            dmx_data[i] = i
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        send_artnet_packet(packet, target_ip)
    
    time.sleep(2)
    
    # 测试模式3: RGB测试
    print("🎨 测试模式3: RGB测试")
    dmx_data = [0] * 512
    for i in range(0, 150, 3):
        dmx_data[i] = 255     # R
        dmx_data[i+1] = 128   # G
        dmx_data[i+2] = 64    # B
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        send_artnet_packet(packet, target_ip)
    
    time.sleep(2)
    
    # 测试模式4: 全亮
    print("💡 测试模式4: 全亮测试")
    dmx_data = [255] * 512
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        send_artnet_packet(packet, target_ip)
    
    time.sleep(2)
    
    # 测试模式5: 全暗
    print("🌑 测试模式5: 全暗测试")
    dmx_data = [0] * 512
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        send_artnet_packet(packet, target_ip)
    
    print()
    print("✅ 所有测试模式已完成")
    print()
    print("🎛️ MA2控台兼容性说明:")
    print("• 确保MA2网络设置正确")
    print("• 检查Universe配置匹配")
    print("• 验证防火墙允许UDP 6454端口")
    print("• 使用千兆以太网获得最佳性能")

def discover_artnet_devices():
    """发现Art-Net设备"""
    print("🔍 正在搜索Art-Net设备...")
    
    try:
        # 创建Art-Net Poll数据包
        header = b"Art-Net\x00"
        opcode = struct.pack("<H", 0x2000)  # OpPoll
        protocol_version = struct.pack(">H", 14)
        flags = struct.pack("B", 0x02)  # Send me ArtPollReply
        priority = struct.pack("B", 0)
        
        poll_packet = header + opcode + protocol_version + flags + priority
        
        # 发送Poll包
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.settimeout(3)
        
        sock.sendto(poll_packet, ('***************', 6454))
        
        devices_found = 0
        start_time = time.time()
        
        while time.time() - start_time < 3:
            try:
                data, addr = sock.recvfrom(1024)
                if len(data) >= 8 and data[:8] == b"Art-Net\x00":
                    devices_found += 1
                    print(f"  📱 发现设备: {addr[0]}")
            except socket.timeout:
                break
            except:
                continue
        
        sock.close()
        
        if devices_found == 0:
            print("  ❌ 未发现Art-Net设备")
        else:
            print(f"  ✅ 总共发现 {devices_found} 个Art-Net设备")
            
    except Exception as e:
        print(f"❌ 设备发现失败: {e}")

if __name__ == "__main__":
    print("🎭 Art-Net专业测试工具启动")
    print("与MA2、GrandMA3、Avolites等专业控台兼容")
    print()
    
    # 发现设备
    discover_artnet_devices()
    print()
    
    # 运行测试
    test_artnet_patterns()
    
    print()
    print("🎯 测试完成！请检查连接的Art-Net设备是否有响应。")
