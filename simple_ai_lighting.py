#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化AI灯光系统 - 功能完整但易用
支持MA XML灯具库 + AI音乐分析 + Art-Net输出
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import socket
import struct
import threading
import time
import random
from datetime import datetime
from enhanced_ma_parser import EnhancedMAParser

class SimpleAILighting:
    """简化AI灯光系统"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 简化AI灯光系统")
        self.root.geometry("1200x800")
        
        # 初始化组件
        self.ma_parser = EnhancedMAParser()
        self.fixtures = []
        self.music_analysis = None
        self.lighting_sequence = []
        self.artnet_socket = None
        self.is_playing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        # 标题
        title_frame = tk.Frame(self.root, bg="#1e293b", height=60)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)
        
        tk.Label(title_frame, text="🎭 简化AI灯光系统", 
                font=("Arial", 18, "bold"), fg="white", bg="#1e293b").pack(expand=True)
        
        # 主要内容
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 左侧控制面板
        self.create_control_panel(main_frame)
        
        # 右侧结果显示
        self.create_results_panel(main_frame)
        
        # 状态栏
        self.status_label = tk.Label(self.root, text="就绪", relief="sunken", anchor="w")
        self.status_label.pack(side="bottom", fill="x")
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = tk.Frame(parent, width=400)
        control_frame.pack(side="left", fill="y", padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 1. MA灯具库导入
        lib_frame = tk.LabelFrame(control_frame, text="1️⃣ MA灯具库", font=("Arial", 12, "bold"))
        lib_frame.pack(fill="x", pady=5)
        
        self.xml_file_var = tk.StringVar()
        tk.Entry(lib_frame, textvariable=self.xml_file_var, width=40).pack(side="left", padx=5, pady=5)
        tk.Button(lib_frame, text="选择XML", command=self.browse_xml).pack(side="right", padx=5, pady=5)
        tk.Button(lib_frame, text="解析", command=self.parse_xml, 
                 bg="#059669", fg="white").pack(side="right", padx=5, pady=5)
        
        # 灯具选择
        tk.Label(lib_frame, text="选择灯具:").pack(anchor="w", padx=5)
        self.fixture_var = tk.StringVar()
        self.fixture_combo = ttk.Combobox(lib_frame, textvariable=self.fixture_var, width=50)
        self.fixture_combo.pack(fill="x", padx=5, pady=2)
        
        # 2. 音乐分析
        music_frame = tk.LabelFrame(control_frame, text="2️⃣ 音乐分析", font=("Arial", 12, "bold"))
        music_frame.pack(fill="x", pady=5)
        
        self.music_file_var = tk.StringVar()
        tk.Entry(music_frame, textvariable=self.music_file_var, width=40).pack(side="left", padx=5, pady=5)
        tk.Button(music_frame, text="选择音乐", command=self.browse_music).pack(side="right", padx=5, pady=5)
        tk.Button(music_frame, text="AI分析", command=self.analyze_music, 
                 bg="#dc2626", fg="white").pack(side="right", padx=5, pady=5)
        
        # 3. 灯具配接
        patch_frame = tk.LabelFrame(control_frame, text="3️⃣ 灯具配接", font=("Arial", 12, "bold"))
        patch_frame.pack(fill="x", pady=5)
        
        # 配接参数
        param_frame = tk.Frame(patch_frame)
        param_frame.pack(fill="x", padx=5, pady=5)
        
        tk.Label(param_frame, text="起始地址:").grid(row=0, column=0, sticky="w")
        self.start_addr_var = tk.StringVar(value="1")
        tk.Entry(param_frame, textvariable=self.start_addr_var, width=10).grid(row=0, column=1, padx=5)
        
        tk.Label(param_frame, text="Universe:").grid(row=0, column=2, sticky="w", padx=(10,0))
        self.universe_var = tk.StringVar(value="1")
        tk.Entry(param_frame, textvariable=self.universe_var, width=10).grid(row=0, column=3, padx=5)
        
        tk.Label(param_frame, text="数量:").grid(row=1, column=0, sticky="w")
        self.count_var = tk.StringVar(value="4")
        tk.Entry(param_frame, textvariable=self.count_var, width=10).grid(row=1, column=1, padx=5)
        
        tk.Button(patch_frame, text="➕ 添加灯具", command=self.add_fixtures,
                 bg="#7c3aed", fg="white").pack(pady=5)
        
        # 4. AI序列生成
        ai_frame = tk.LabelFrame(control_frame, text="4️⃣ AI序列生成", font=("Arial", 12, "bold"))
        ai_frame.pack(fill="x", pady=5)
        
        # AI参数
        tk.Label(ai_frame, text="创意级别:").pack(anchor="w", padx=5)
        self.creativity_var = tk.StringVar(value="中等")
        ttk.Combobox(ai_frame, textvariable=self.creativity_var, 
                    values=["保守", "中等", "创新"], width=20).pack(padx=5, pady=2)
        
        tk.Label(ai_frame, text="效果强度:").pack(anchor="w", padx=5)
        self.intensity_var = tk.DoubleVar(value=0.8)
        tk.Scale(ai_frame, variable=self.intensity_var, from_=0.1, to=1.0, 
                resolution=0.1, orient="horizontal").pack(fill="x", padx=5)
        
        tk.Button(ai_frame, text="🤖 生成AI序列", command=self.generate_sequence,
                 bg="#f59e0b", fg="white", font=("Arial", 12, "bold")).pack(pady=10)
        
        # 5. Art-Net播放
        artnet_frame = tk.LabelFrame(control_frame, text="5️⃣ Art-Net播放", font=("Arial", 12, "bold"))
        artnet_frame.pack(fill="x", pady=5)
        
        # 连接设置
        conn_frame = tk.Frame(artnet_frame)
        conn_frame.pack(fill="x", padx=5, pady=5)
        
        tk.Label(conn_frame, text="MA控台IP:").pack(side="left")
        self.artnet_ip_var = tk.StringVar(value="*********")
        tk.Entry(conn_frame, textvariable=self.artnet_ip_var, width=15).pack(side="left", padx=5)
        tk.Button(conn_frame, text="连接", command=self.connect_artnet).pack(side="left", padx=5)
        
        # 播放控制
        play_frame = tk.Frame(artnet_frame)
        play_frame.pack(fill="x", padx=5, pady=5)
        
        self.play_btn = tk.Button(play_frame, text="▶️ 播放", command=self.start_playback,
                                 bg="#059669", fg="white", font=("Arial", 12, "bold"))
        self.play_btn.pack(side="left", padx=5)
        
        tk.Button(play_frame, text="⏹️ 停止", command=self.stop_playback,
                 bg="#dc2626", fg="white").pack(side="left", padx=5)
        
        # 连接状态
        self.connection_status = tk.Label(artnet_frame, text="🔴 未连接", font=("Arial", 10, "bold"))
        self.connection_status.pack(pady=5)
        
    def create_results_panel(self, parent):
        """创建结果显示面板"""
        results_frame = tk.Frame(parent)
        results_frame.pack(side="right", fill="both", expand=True)
        
        # 创建选项卡
        notebook = ttk.Notebook(results_frame)
        notebook.pack(fill="both", expand=True)
        
        # 灯具库选项卡
        lib_tab = ttk.Frame(notebook)
        notebook.add(lib_tab, text="📚 灯具库")
        
        self.lib_text = tk.Text(lib_tab, font=("Consolas", 10))
        lib_scroll = ttk.Scrollbar(lib_tab, command=self.lib_text.yview)
        self.lib_text.config(yscrollcommand=lib_scroll.set)
        self.lib_text.pack(side="left", fill="both", expand=True)
        lib_scroll.pack(side="right", fill="y")
        
        # 音乐分析选项卡
        music_tab = ttk.Frame(notebook)
        notebook.add(music_tab, text="🎵 音乐分析")
        
        self.music_text = tk.Text(music_tab, font=("Consolas", 10))
        music_scroll = ttk.Scrollbar(music_tab, command=self.music_text.yview)
        self.music_text.config(yscrollcommand=music_scroll.set)
        self.music_text.pack(side="left", fill="both", expand=True)
        music_scroll.pack(side="right", fill="y")
        
        # 配接列表选项卡
        patch_tab = ttk.Frame(notebook)
        notebook.add(patch_tab, text="🔌 配接列表")
        
        columns = ("名称", "类型", "地址", "Universe", "通道数")
        self.patch_tree = ttk.Treeview(patch_tab, columns=columns, show="headings")
        for col in columns:
            self.patch_tree.heading(col, text=col)
            self.patch_tree.column(col, width=100)
        
        patch_scroll = ttk.Scrollbar(patch_tab, command=self.patch_tree.yview)
        self.patch_tree.config(yscrollcommand=patch_scroll.set)
        self.patch_tree.pack(side="left", fill="both", expand=True)
        patch_scroll.pack(side="right", fill="y")
        
        # AI序列选项卡
        seq_tab = ttk.Frame(notebook)
        notebook.add(seq_tab, text="🤖 AI序列")
        
        self.seq_text = tk.Text(seq_tab, font=("Consolas", 10))
        seq_scroll = ttk.Scrollbar(seq_tab, command=self.seq_text.yview)
        self.seq_text.config(yscrollcommand=seq_scroll.set)
        self.seq_text.pack(side="left", fill="both", expand=True)
        seq_scroll.pack(side="right", fill="y")
        
        # 实时状态选项卡
        status_tab = ttk.Frame(notebook)
        notebook.add(status_tab, text="📊 实时状态")
        
        self.status_text = tk.Text(status_tab, font=("Consolas", 10))
        status_text_scroll = ttk.Scrollbar(status_tab, command=self.status_text.yview)
        self.status_text.config(yscrollcommand=status_text_scroll.set)
        self.status_text.pack(side="left", fill="both", expand=True)
        status_text_scroll.pack(side="right", fill="y")
        
    # ==================== 功能实现 ====================
    
    def browse_xml(self):
        """选择XML文件"""
        file_path = filedialog.askopenfilename(
            title="选择MA XML灯具库文件",
            filetypes=[("XML文件", "*.xml"), ("所有文件", "*.*")]
        )
        if file_path:
            self.xml_file_var.set(file_path)
    
    def parse_xml(self):
        """解析XML灯具库"""
        xml_file = self.xml_file_var.get()
        if not xml_file:
            messagebox.showwarning("警告", "请先选择XML文件")
            return
        
        try:
            self.status_label.config(text="正在解析MA XML灯具库...")
            
            if self.ma_parser.parse_xml_file(xml_file):
                # 更新灯具下拉框
                fixture_names = []
                for fixture in self.ma_parser.fixtures.values():
                    name = f"{fixture['manufacturer']} - {fixture['name']}"
                    fixture_names.append(name)
                
                self.fixture_combo['values'] = fixture_names
                if fixture_names:
                    self.fixture_combo.set(fixture_names[0])
                
                # 显示解析结果
                stats = self.ma_parser.get_statistics()
                result_text = f"✅ MA XML解析成功！\n\n"
                result_text += f"总灯具数: {stats['total_fixtures']}\n"
                result_text += f"制造商数: {stats['manufacturer_count']}\n"
                result_text += f"制造商: {', '.join(stats['manufacturers'])}\n\n"
                
                result_text += "灯具列表:\n"
                result_text += "=" * 50 + "\n"
                
                for fixture in self.ma_parser.fixtures.values():
                    result_text += f"{fixture['manufacturer']} - {fixture['name']}\n"
                    result_text += f"  通道数: {fixture['channel_count']}\n"
                    result_text += f"  通道: {', '.join(fixture['channels'].keys())}\n\n"
                
                self.lib_text.delete("1.0", "end")
                self.lib_text.insert("1.0", result_text)
                
                self.status_label.config(text=f"成功解析 {stats['total_fixtures']} 个灯具")
                
            else:
                messagebox.showerror("错误", "XML解析失败")
                
        except Exception as e:
            messagebox.showerror("错误", f"解析过程出错: {e}")
    
    def browse_music(self):
        """选择音乐文件"""
        file_path = filedialog.askopenfilename(
            title="选择音乐文件",
            filetypes=[("音频文件", "*.mp3 *.wav *.flac"), ("所有文件", "*.*")]
        )
        if file_path:
            self.music_file_var.set(file_path)
    
    def analyze_music(self):
        """AI音乐分析"""
        music_file = self.music_file_var.get()
        if not music_file:
            messagebox.showwarning("警告", "请先选择音乐文件")
            return
        
        try:
            self.status_label.config(text="AI正在分析音乐...")
            
            # 模拟AI分析
            bpm = random.uniform(80, 160)
            duration = random.uniform(180, 300)
            
            # 生成节拍时间点
            beat_interval = 60 / bpm
            beat_times = []
            current_time = 0
            while current_time < duration:
                beat_times.append(current_time)
                current_time += beat_interval
            
            # 分析结果
            if bpm < 90:
                style, mood = "慢板/抒情", "平静/舒缓"
            elif bpm < 120:
                style, mood = "中板/流行", "稳定/平衡"
            elif bpm < 140:
                style, mood = "快板/摇滚", "动感/活跃"
            else:
                style, mood = "电子/舞曲", "激昂/兴奋"
            
            self.music_analysis = {
                'file_path': music_file,
                'bpm': bpm,
                'duration': duration,
                'beat_times': beat_times,
                'style': style,
                'mood': mood,
                'energy_level': random.uniform(0.3, 0.9)
            }
            
            # 显示分析结果
            result_text = f"🎵 AI音乐分析结果\n"
            result_text += "=" * 50 + "\n\n"
            result_text += f"文件: {music_file}\n"
            result_text += f"BPM: {bpm:.1f}\n"
            result_text += f"时长: {duration:.1f}秒\n"
            result_text += f"节拍数: {len(beat_times)}\n"
            result_text += f"风格: {style}\n"
            result_text += f"情绪: {mood}\n"
            result_text += f"能量级别: {self.music_analysis['energy_level']:.2f}\n\n"
            
            result_text += "前20个节拍时间点:\n"
            for i, beat_time in enumerate(beat_times[:20]):
                result_text += f"  节拍 {i+1:2d}: {beat_time:6.2f}s\n"
            
            self.music_text.delete("1.0", "end")
            self.music_text.insert("1.0", result_text)
            
            self.status_label.config(text="音乐分析完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"音乐分析失败: {e}")
    
    def add_fixtures(self):
        """添加灯具配接"""
        if not self.fixture_var.get():
            messagebox.showwarning("警告", "请先选择灯具类型")
            return
        
        try:
            # 解析选择的灯具
            selected = self.fixture_var.get()
            manufacturer, fixture_name = selected.split(" - ", 1)
            
            # 查找灯具定义
            fixture_def = None
            for f in self.ma_parser.fixtures.values():
                if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                    fixture_def = f
                    break
            
            if not fixture_def:
                messagebox.showerror("错误", "找不到灯具定义")
                return
            
            # 获取参数
            start_addr = int(self.start_addr_var.get())
            universe = int(self.universe_var.get())
            count = int(self.count_var.get())
            
            # 添加多个灯具
            for i in range(count):
                fixture = {
                    'id': len(self.fixtures) + 1,
                    'name': f"{fixture_name}_{i+1}",
                    'type': fixture_def['id'],
                    'manufacturer': manufacturer,
                    'start_address': start_addr + i * fixture_def['channel_count'],
                    'universe': universe,
                    'channel_count': fixture_def['channel_count'],
                    'channels': fixture_def['channels']
                }
                self.fixtures.append(fixture)
            
            # 更新显示
            self.update_patch_display()
            
            # 自动递增地址
            next_addr = start_addr + count * fixture_def['channel_count']
            self.start_addr_var.set(str(next_addr))
            
            self.status_label.config(text=f"已添加 {count} 个 {fixture_name}")
            
        except Exception as e:
            messagebox.showerror("错误", f"添加灯具失败: {e}")
    
    def update_patch_display(self):
        """更新配接显示"""
        # 清除现有数据
        for item in self.patch_tree.get_children():
            self.patch_tree.delete(item)
        
        # 添加配接数据
        for fixture in self.fixtures:
            self.patch_tree.insert("", "end", values=(
                fixture['name'],
                f"{fixture['manufacturer']} {fixture['type']}",
                f"{fixture['start_address']}-{fixture['start_address'] + fixture['channel_count'] - 1}",
                fixture['universe'],
                fixture['channel_count']
            ))

    def generate_sequence(self):
        """生成AI灯光序列"""
        if not self.music_analysis:
            messagebox.showwarning("警告", "请先完成音乐分析")
            return

        if not self.fixtures:
            messagebox.showwarning("警告", "请先添加灯具配接")
            return

        try:
            self.status_label.config(text="AI正在生成灯光序列...")

            beat_times = self.music_analysis['beat_times']
            style = self.music_analysis['style']
            mood = self.music_analysis['mood']
            energy = self.music_analysis['energy_level']
            creativity = self.creativity_var.get()
            intensity = self.intensity_var.get()

            # 根据风格选择色彩方案
            color_schemes = {
                "慢板/抒情": ["#FFE4B5", "#DDA0DD", "#87CEEB"],  # 暖色调
                "中板/流行": ["#FF6347", "#32CD32", "#1E90FF"],  # 鲜艳色
                "快板/摇滚": ["#FF0000", "#FFFF00", "#FFFFFF"],  # 强烈对比
                "电子/舞曲": ["#FF00FF", "#00FFFF", "#00FF00"]   # 霓虹色
            }

            colors = color_schemes.get(style, color_schemes["中板/流行"])

            # 生成序列
            self.lighting_sequence = []

            for i, beat_time in enumerate(beat_times):
                # 根据创意级别决定变化频率
                change_freq = {"保守": 4, "中等": 2, "创新": 1}[creativity]

                if i % change_freq == 0:  # 按频率变化
                    for fixture in self.fixtures:
                        # 生成DMX值
                        dmx_values = self.generate_dmx_values(fixture, colors, i, energy, intensity)

                        event = {
                            'time': beat_time,
                            'fixture_id': fixture['id'],
                            'fixture_name': fixture['name'],
                            'universe': fixture['universe'],
                            'start_address': fixture['start_address'],
                            'dmx_values': dmx_values
                        }

                        self.lighting_sequence.append(event)

            # 显示序列
            self.display_sequence()

            self.status_label.config(text=f"AI序列生成完成，共 {len(self.lighting_sequence)} 个事件")

        except Exception as e:
            messagebox.showerror("错误", f"AI序列生成失败: {e}")

    def generate_dmx_values(self, fixture, colors, beat_index, energy, intensity):
        """生成DMX值"""
        dmx_values = {}
        channels = fixture['channels']

        # 调光通道
        if 'dimmer' in channels:
            dmx_values['dimmer'] = int(255 * intensity * energy)

        # RGB颜色通道
        if all(ch in channels for ch in ['red', 'green', 'blue']):
            color = colors[beat_index % len(colors)]
            rgb = self.hex_to_rgb(color)

            dmx_values['red'] = int(rgb[0] * intensity)
            dmx_values['green'] = int(rgb[1] * intensity)
            dmx_values['blue'] = int(rgb[2] * intensity)

        # 白光通道
        if 'white' in channels:
            dmx_values['white'] = int(128 * intensity)

        # 移动通道（摇头灯）
        if 'pan' in channels:
            pan_value = 127 + int(100 * energy * (1 if beat_index % 2 == 0 else -1))
            dmx_values['pan'] = max(0, min(255, pan_value))

        if 'tilt' in channels:
            tilt_value = 127 + int(50 * energy * (1 if beat_index % 4 < 2 else -1))
            dmx_values['tilt'] = max(0, min(255, tilt_value))

        # 频闪通道
        if 'strobe' in channels and energy > 0.7:
            dmx_values['strobe'] = int(200 + 55 * energy)
        elif 'strobe' in channels:
            dmx_values['strobe'] = 0

        return dmx_values

    def hex_to_rgb(self, hex_color):
        """十六进制颜色转RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def display_sequence(self):
        """显示序列"""
        result_text = f"🤖 AI灯光序列\n"
        result_text += "=" * 50 + "\n\n"
        result_text += f"总事件数: {len(self.lighting_sequence)}\n"
        result_text += f"音乐风格: {self.music_analysis['style']}\n"
        result_text += f"情绪: {self.music_analysis['mood']}\n"
        result_text += f"创意级别: {self.creativity_var.get()}\n"
        result_text += f"效果强度: {self.intensity_var.get():.1f}\n\n"

        result_text += "序列预览 (前20个事件):\n"
        result_text += "-" * 50 + "\n"

        for i, event in enumerate(self.lighting_sequence[:20]):
            result_text += f"时间: {event['time']:6.2f}s | "
            result_text += f"灯具: {event['fixture_name']} | "
            result_text += f"Universe: {event['universe']} | "
            result_text += f"地址: {event['start_address']}\n"

            # 显示DMX值
            dmx_str = ", ".join([f"{ch}:{val}" for ch, val in event['dmx_values'].items()])
            result_text += f"  DMX: {dmx_str}\n\n"

        if len(self.lighting_sequence) > 20:
            result_text += f"... 还有 {len(self.lighting_sequence) - 20} 个事件\n"

        self.seq_text.delete("1.0", "end")
        self.seq_text.insert("1.0", result_text)

    def connect_artnet(self):
        """连接Art-Net"""
        try:
            target_ip = self.artnet_ip_var.get()

            # 创建UDP socket
            self.artnet_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.artnet_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)

            # 测试连接
            test_packet = self.create_artnet_packet(1, [0] * 512)
            self.artnet_socket.sendto(test_packet, (target_ip, 6454))

            self.connection_status.config(text="🟢 已连接", fg="green")
            self.status_label.config(text=f"Art-Net已连接: {target_ip}")

            messagebox.showinfo("成功", f"Art-Net连接成功！\n目标: {target_ip}:6454")

        except Exception as e:
            messagebox.showerror("错误", f"Art-Net连接失败: {e}")
            self.connection_status.config(text="🔴 连接失败", fg="red")

    def create_artnet_packet(self, universe, dmx_data):
        """创建Art-Net数据包"""
        packet = bytearray()
        packet.extend(b"Art-Net\x00")  # Art-Net ID
        packet.extend(struct.pack("<H", 0x5000))  # OpDmx
        packet.extend(struct.pack(">H", 14))  # Protocol version
        packet.append(0)  # Sequence
        packet.append(0)  # Physical
        packet.append(universe & 0xFF)  # Universe low
        packet.append((universe >> 8) & 0xFF)  # Universe high
        packet.extend(struct.pack(">H", 512))  # Data length

        # 确保DMX数据长度为512
        if len(dmx_data) < 512:
            dmx_data.extend([0] * (512 - len(dmx_data)))
        elif len(dmx_data) > 512:
            dmx_data = dmx_data[:512]

        packet.extend(dmx_data)
        return packet

    def start_playback(self):
        """开始播放"""
        if not self.lighting_sequence:
            messagebox.showwarning("警告", "请先生成AI灯光序列")
            return

        if not self.artnet_socket:
            messagebox.showwarning("警告", "请先连接Art-Net")
            return

        if not self.is_playing:
            self.is_playing = True
            self.play_btn.config(text="⏸️ 暂停")

            # 在新线程中播放
            playback_thread = threading.Thread(target=self.playback_thread, daemon=True)
            playback_thread.start()

            self.status_label.config(text="开始播放AI灯光序列...")

    def stop_playback(self):
        """停止播放"""
        self.is_playing = False
        self.play_btn.config(text="▶️ 播放")
        self.status_label.config(text="播放已停止")

        # 清除所有输出
        if self.artnet_socket:
            try:
                universes = set(f['universe'] for f in self.fixtures)
                for universe in universes:
                    packet = self.create_artnet_packet(universe, [0] * 512)
                    target_ip = self.artnet_ip_var.get()
                    self.artnet_socket.sendto(packet, (target_ip, 6454))
            except:
                pass

    def playback_thread(self):
        """播放线程"""
        try:
            start_time = time.time()
            sorted_sequence = sorted(self.lighting_sequence, key=lambda x: x['time'])

            event_index = 0
            sent_packets = 0

            while self.is_playing and event_index < len(sorted_sequence):
                current_time = time.time() - start_time

                # 处理当前时间的事件
                events_to_send = []
                while (event_index < len(sorted_sequence) and
                       sorted_sequence[event_index]['time'] <= current_time):
                    events_to_send.append(sorted_sequence[event_index])
                    event_index += 1

                # 发送Art-Net数据
                if events_to_send:
                    universes_data = self.prepare_universe_data(events_to_send)

                    for universe, dmx_data in universes_data.items():
                        try:
                            packet = self.create_artnet_packet(universe, dmx_data)
                            target_ip = self.artnet_ip_var.get()
                            self.artnet_socket.sendto(packet, (target_ip, 6454))
                            sent_packets += 1
                        except Exception as e:
                            print(f"发送错误: {e}")

                    # 更新状态显示
                    status_text = f"播放中... 时间: {current_time:.1f}s | 发送包数: {sent_packets}\n"
                    status_text += f"活跃事件: {len(events_to_send)}\n\n"

                    for event in events_to_send[-5:]:  # 显示最近5个事件
                        status_text += f"{event['fixture_name']}: "
                        status_text += ", ".join([f"{ch}={val}" for ch, val in event['dmx_values'].items()])
                        status_text += "\n"

                    self.root.after(0, lambda: self.update_status_display(status_text))

                # 检查是否播放完成
                if current_time >= self.music_analysis['duration']:
                    break

                time.sleep(0.02)  # 50Hz更新频率

            # 播放完成
            self.root.after(0, self.stop_playback)

        except Exception as e:
            print(f"播放线程错误: {e}")
            self.root.after(0, self.stop_playback)

    def prepare_universe_data(self, events):
        """准备Universe数据"""
        universes_data = {}

        for event in events:
            universe = event['universe']
            start_address = event['start_address']
            dmx_values = event['dmx_values']

            # 初始化Universe数据
            if universe not in universes_data:
                universes_data[universe] = [0] * 512

            # 查找灯具定义
            fixture = None
            for f in self.fixtures:
                if f['id'] == event['fixture_id']:
                    fixture = f
                    break

            if fixture:
                # 映射通道到DMX地址
                channel_names = list(fixture['channels'].keys())

                for channel_name, value in dmx_values.items():
                    if channel_name in channel_names:
                        channel_offset = channel_names.index(channel_name)
                        dmx_address = start_address + channel_offset - 1  # 转换为0基址

                        if 0 <= dmx_address < 512:
                            universes_data[universe][dmx_address] = max(0, min(255, int(value)))

        return universes_data

    def update_status_display(self, text):
        """更新状态显示"""
        self.status_text.delete("1.0", "end")
        self.status_text.insert("1.0", text)

    def run(self):
        """运行应用"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭应用"""
        if self.is_playing:
            self.stop_playback()

        if self.artnet_socket:
            self.artnet_socket.close()

        self.root.destroy()

if __name__ == "__main__":
    print("🎭 简化AI灯光系统启动中...")

    try:
        app = SimpleAILighting()
        print("✅ 系统初始化完成")
        print("\n💡 使用步骤:")
        print("   1. 📚 选择并解析MA XML灯具库")
        print("   2. 🎵 选择音乐文件并AI分析")
        print("   3. 🔌 添加灯具配接")
        print("   4. 🤖 生成AI灯光序列")
        print("   5. 🔗 连接Art-Net到MA控台")
        print("   6. ▶️ 播放灯光效果")

        app.run()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
