#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
功能验证脚本 - 验证主程序的新增功能
"""

import socket
import struct
import time
import threading
import json
from datetime import datetime

def test_main_program_features():
    """测试主程序的新增功能"""
    print("🎯 主程序功能验证")
    print("=" * 50)
    
    features_status = {}
    
    # 1. 验证Art-Net监听器是否在运行
    print("\n1️⃣ 验证Art-Net监听器...")
    try:
        # 检查6454端口是否被占用（说明监听器在运行）
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        try:
            sock.bind(('', 6454))
            sock.close()
            features_status["Art-Net监听器"] = "❌ 未运行 - 端口6454未被占用"
        except OSError:
            features_status["Art-Net监听器"] = "✅ 正在运行 - 端口6454被占用"
    except Exception as e:
        features_status["Art-Net监听器"] = f"⚠️ 检测失败: {e}"
    
    # 2. 测试Art-Net发送功能
    print("2️⃣ 测试Art-Net发送功能...")
    try:
        # 创建测试Art-Net包
        packet = bytearray()
        packet.extend(b"Art-Net\x00")
        packet.extend(struct.pack("<H", 0x5000))
        packet.extend(struct.pack(">H", 14))
        packet.append(1)  # Sequence
        packet.append(0)  # Physical
        packet.append(0)  # Universe low
        packet.append(0)  # Universe high
        packet.extend(struct.pack(">H", 512))
        
        # 测试DMX数据
        dmx_data = [0] * 512
        dmx_data[0] = 255  # 测试通道1
        dmx_data[1] = 128  # 测试通道2
        packet.extend(dmx_data)
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.sendto(packet, ("255.255.255.255", 6454))
        sock.close()
        
        features_status["Art-Net发送"] = "✅ 功能正常 - 测试包发送成功"
    except Exception as e:
        features_status["Art-Net发送"] = f"❌ 发送失败: {e}"
    
    # 3. 测试MIDI时间码发送
    print("3️⃣ 测试MIDI时间码...")
    try:
        import pygame.midi
        if not pygame.midi.get_init():
            pygame.midi.init()
        
        device_count = pygame.midi.get_count()
        if device_count > 0:
            # 尝试发送测试MTC
            mtc_data = [0xF0, 0x7F, 0x7F, 0x01, 0x01, 0, 0, 30, 0, 0xF7]
            
            for i in range(device_count):
                info = pygame.midi.get_device_info(i)
                if info[3]:  # 输出设备
                    try:
                        midi_out = pygame.midi.Output(i)
                        for byte in mtc_data:
                            midi_out.write_short(byte)
                        midi_out.close()
                        features_status["MIDI时间码"] = "✅ 功能正常 - MTC发送成功"
                        break
                    except:
                        continue
            else:
                features_status["MIDI时间码"] = "⚠️ 无可用MIDI输出设备"
        else:
            features_status["MIDI时间码"] = "⚠️ 未检测到MIDI设备"
        
        pygame.midi.quit()
    except Exception as e:
        features_status["MIDI时间码"] = f"❌ 测试失败: {e}"
    
    # 4. 测试网络时间码发送
    print("4️⃣ 测试网络时间码...")
    try:
        # 测试SMPTE发送
        smpte_data = "SMPTE:00:00:45:00"
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.sendto(smpte_data.encode(), ("127.0.0.1", 8000))
        
        # 测试OSC发送
        osc_data = "/timecode 0 0 45 0"
        sock.sendto(osc_data.encode(), ("127.0.0.1", 9000))
        sock.close()
        
        features_status["网络时间码"] = "✅ 功能正常 - SMPTE和OSC发送成功"
    except Exception as e:
        features_status["网络时间码"] = f"❌ 发送失败: {e}"
    
    # 5. 测试虚拟MIDI功能
    print("5️⃣ 测试虚拟MIDI...")
    try:
        import mido
        
        # 检查现有端口
        output_ports = mido.get_output_names()
        
        if output_ports:
            # 尝试使用第一个可用端口
            port = mido.open_output(output_ports[0])
            mtc_msg = mido.Message('sysex', data=[0x7F, 0x7F, 0x01, 0x01, 0, 1, 0, 0])
            port.send(mtc_msg)
            port.close()
            features_status["虚拟MIDI"] = f"✅ 功能正常 - 使用端口: {output_ports[0]}"
        else:
            features_status["虚拟MIDI"] = "⚠️ 无可用MIDI端口"
    except Exception as e:
        features_status["虚拟MIDI"] = f"❌ 测试失败: {e}"
    
    # 6. 测试MSC命令发送
    print("6️⃣ 测试MSC命令...")
    try:
        # MSC GO命令
        msc_go = [0xF0, 0x7F, 0x00, 0x02, 0x7F, 0x01, 0x01, 0xF7]
        
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.sendto(bytes(msc_go), ("127.0.0.1", 6000))
        sock.close()
        
        features_status["MSC命令"] = "✅ 功能正常 - MSC GO命令发送成功"
    except Exception as e:
        features_status["MSC命令"] = f"❌ 发送失败: {e}"
    
    # 生成报告
    print("\n" + "=" * 50)
    print("📋 功能验证报告")
    print("=" * 50)
    
    working_count = 0
    warning_count = 0
    failed_count = 0
    
    for feature, status in features_status.items():
        print(f"{feature}: {status}")
        if "✅" in status:
            working_count += 1
        elif "⚠️" in status:
            warning_count += 1
        else:
            failed_count += 1
    
    print(f"\n📊 统计:")
    print(f"   正常工作: {working_count}")
    print(f"   需要注意: {warning_count}")
    print(f"   存在问题: {failed_count}")
    
    # 保存验证报告
    verification_report = {
        "timestamp": datetime.now().isoformat(),
        "features": features_status,
        "summary": {
            "working": working_count,
            "warning": warning_count,
            "failed": failed_count
        }
    }
    
    with open("feature_verification_report.json", "w", encoding="utf-8") as f:
        json.dump(verification_report, f, indent=4, ensure_ascii=False)
    
    print(f"\n💾 验证报告已保存到: feature_verification_report.json")
    
    # 总体评估
    if failed_count == 0:
        if warning_count == 0:
            print("\n🎉 所有功能完美运行！")
        else:
            print(f"\n✅ 核心功能正常，{warning_count}个功能需要配置")
    else:
        print(f"\n⚠️ {failed_count}个功能存在问题需要修复")

def test_ma2_connectivity():
    """测试与MA2控台的连接性"""
    print("\n🔗 MA2控台连接性测试")
    print("=" * 30)
    
    ma2_ip = "*********"
    
    # 测试Art-Net连接
    print("测试Art-Net连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(2.0)
        
        # 发送Art-Net Poll请求
        poll_packet = bytearray()
        poll_packet.extend(b"Art-Net\x00")
        poll_packet.extend(struct.pack("<H", 0x2000))  # OpPoll
        poll_packet.extend(struct.pack(">H", 14))
        poll_packet.append(0x02)  # Flags
        poll_packet.append(0x00)  # Priority
        
        sock.sendto(poll_packet, (ma2_ip, 6454))
        
        # 等待回复
        try:
            data, addr = sock.recvfrom(1024)
            if addr[0] == ma2_ip:
                print(f"✅ MA2 Art-Net连接正常 - 收到来自 {addr[0]} 的回复")
            else:
                print(f"⚠️ 收到其他设备回复: {addr[0]}")
        except socket.timeout:
            print("❌ MA2 Art-Net连接超时")
        
        sock.close()
    except Exception as e:
        print(f"❌ Art-Net连接测试失败: {e}")
    
    # 测试时间码端口
    print("测试时间码端口...")
    timecode_ports = [8000, 9000]
    for port in timecode_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.sendto(b"TEST_TIMECODE", (ma2_ip, port))
            sock.close()
            print(f"✅ 端口 {port} 连接正常")
        except Exception as e:
            print(f"❌ 端口 {port} 连接失败: {e}")

if __name__ == "__main__":
    test_main_program_features()
    test_ma2_connectivity()
