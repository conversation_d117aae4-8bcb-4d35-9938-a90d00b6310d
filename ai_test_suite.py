#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI辅助系统测试套件
验证核心算法和功能
"""

import json
import time
import random
from datetime import datetime
from typing import Dict, List

class AITestSuite:
    """AI系统测试套件"""
    
    def __init__(self):
        self.test_results = []
        
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 AI辅助系统测试套件")
        print("=" * 50)
        
        tests = [
            ("音乐节拍检测算法", self.test_beat_detection),
            ("音乐风格分类", self.test_style_classification),
            ("情绪分析算法", self.test_mood_analysis),
            ("色彩方案生成", self.test_color_palette_generation),
            ("灯光效果推荐", self.test_lighting_recommendation),
            ("场景自动生成", self.test_scene_generation),
            ("时间轴计算", self.test_timeline_calculation),
            ("同步点生成", self.test_sync_point_generation)
        ]
        
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            try:
                start_time = time.time()
                result = test_func()
                end_time = time.time()
                
                if result["success"]:
                    print(f"✅ 通过 ({end_time - start_time:.3f}s)")
                    print(f"   {result['message']}")
                else:
                    print(f"❌ 失败 ({end_time - start_time:.3f}s)")
                    print(f"   {result['message']}")
                
                self.test_results.append({
                    "test_name": test_name,
                    "success": result["success"],
                    "message": result["message"],
                    "duration": end_time - start_time,
                    "details": result.get("details", {})
                })
                
            except Exception as e:
                print(f"❌ 异常: {e}")
                self.test_results.append({
                    "test_name": test_name,
                    "success": False,
                    "message": f"测试异常: {e}",
                    "duration": 0,
                    "details": {}
                })
        
        self.generate_test_report()
    
    def test_beat_detection(self) -> Dict:
        """测试节拍检测算法"""
        # 模拟不同BPM的测试
        test_cases = [
            {"bpm": 60, "duration": 120},   # 慢板
            {"bpm": 120, "duration": 180},  # 中板
            {"bpm": 140, "duration": 240},  # 快板
            {"bpm": 180, "duration": 300}   # 极快
        ]
        
        success_count = 0
        total_beats = 0
        
        for case in test_cases:
            bpm = case["bpm"]
            duration = case["duration"]
            
            # 计算理论节拍数
            expected_beats = int(duration * bpm / 60)
            
            # 模拟节拍检测
            beat_interval = 60 / bpm
            detected_beats = []
            
            current_time = 0
            while current_time < duration:
                # 添加一些随机误差模拟真实检测
                error = random.uniform(-0.02, 0.02)  # ±20ms误差
                detected_beats.append(current_time + error)
                current_time += beat_interval
            
            # 验证检测精度
            accuracy = len(detected_beats) / expected_beats
            if 0.95 <= accuracy <= 1.05:  # 允许5%误差
                success_count += 1
            
            total_beats += len(detected_beats)
        
        success_rate = success_count / len(test_cases)
        
        return {
            "success": success_rate >= 0.8,
            "message": f"节拍检测成功率: {success_rate:.2%}, 总检测节拍: {total_beats}",
            "details": {
                "success_rate": success_rate,
                "total_beats": total_beats,
                "test_cases": len(test_cases)
            }
        }
    
    def test_style_classification(self) -> Dict:
        """测试音乐风格分类"""
        test_styles = [
            {"tempo": 65, "energy": 0.05, "brightness": 1200, "expected": "慢板/抒情"},
            {"tempo": 95, "energy": 0.12, "brightness": 2500, "expected": "中板/流行"},
            {"tempo": 125, "energy": 0.18, "brightness": 3200, "expected": "快板/摇滚"},
            {"tempo": 145, "energy": 0.25, "brightness": 3800, "expected": "舞曲/电子"},
            {"tempo": 85, "energy": 0.08, "brightness": 1800, "expected": "民谣/古典"}
        ]
        
        correct_classifications = 0
        
        for test in test_styles:
            # 模拟风格分类算法
            classified_style = self._classify_music_style(
                test["tempo"], test["energy"], test["brightness"]
            )
            
            if classified_style == test["expected"]:
                correct_classifications += 1
        
        accuracy = correct_classifications / len(test_styles)
        
        return {
            "success": accuracy >= 0.8,
            "message": f"风格分类准确率: {accuracy:.2%} ({correct_classifications}/{len(test_styles)})",
            "details": {
                "accuracy": accuracy,
                "correct": correct_classifications,
                "total": len(test_styles)
            }
        }
    
    def test_mood_analysis(self) -> Dict:
        """测试情绪分析"""
        test_moods = [
            {"energy": 0.2, "brightness": 3000, "expected": "激昂/兴奋"},
            {"energy": 0.06, "brightness": 1200, "expected": "平静/忧郁"},
            {"energy": 0.15, "brightness": 2800, "expected": "活跃/欢快"},
            {"energy": 0.08, "brightness": 1800, "expected": "温和/舒缓"},
            {"energy": 0.11, "brightness": 2200, "expected": "中性/平衡"}
        ]
        
        correct_moods = 0
        
        for test in test_moods:
            # 模拟情绪分析算法
            analyzed_mood = self._analyze_mood(test["energy"], test["brightness"])
            
            if analyzed_mood == test["expected"]:
                correct_moods += 1
        
        accuracy = correct_moods / len(test_moods)
        
        return {
            "success": accuracy >= 0.7,
            "message": f"情绪分析准确率: {accuracy:.2%} ({correct_moods}/{len(test_moods)})",
            "details": {
                "accuracy": accuracy,
                "correct": correct_moods,
                "total": len(test_moods)
            }
        }
    
    def test_color_palette_generation(self) -> Dict:
        """测试色彩方案生成"""
        moods = ["激昂/兴奋", "平静/忧郁", "活跃/欢快", "温和/舒缓"]
        
        generated_palettes = 0
        valid_colors = 0
        
        for mood in moods:
            palette = self._generate_color_palette(mood)
            
            if palette and len(palette) >= 3:
                generated_palettes += 1
                
                # 验证颜色格式
                for color in palette:
                    if self._is_valid_hex_color(color):
                        valid_colors += 1
        
        success_rate = generated_palettes / len(moods)
        color_validity = valid_colors / (len(moods) * 5) if len(moods) > 0 else 0
        
        return {
            "success": success_rate >= 0.8 and color_validity >= 0.9,
            "message": f"色彩方案生成率: {success_rate:.2%}, 颜色有效性: {color_validity:.2%}",
            "details": {
                "generation_rate": success_rate,
                "color_validity": color_validity,
                "total_colors": valid_colors
            }
        }
    
    def test_lighting_recommendation(self) -> Dict:
        """测试灯光效果推荐"""
        # 模拟音乐分析数据
        music_data = {
            "tempo": 120,
            "duration": 240,
            "music_style": "快板/摇滚",
            "mood": "激昂/兴奋",
            "beat_times": [i * 0.5 for i in range(480)],  # 120 BPM = 0.5s间隔
            "energy_level": 0.8,
            "brightness": 3200
        }
        
        # 生成推荐
        recommendation = self._generate_lighting_recommendation(music_data)
        
        # 验证推荐质量
        checks = [
            len(recommendation.get("lighting_sequence", [])) > 0,
            "color_palette" in recommendation,
            recommendation.get("confidence", 0) > 0.5,
            len(recommendation.get("color_palette", [])) >= 3
        ]
        
        success_rate = sum(checks) / len(checks)
        
        return {
            "success": success_rate >= 0.8,
            "message": f"灯光推荐质量: {success_rate:.2%}, 生成事件: {len(recommendation.get('lighting_sequence', []))}",
            "details": {
                "quality_score": success_rate,
                "event_count": len(recommendation.get("lighting_sequence", [])),
                "confidence": recommendation.get("confidence", 0)
            }
        }
    
    def test_scene_generation(self) -> Dict:
        """测试场景自动生成"""
        scene_types = ["音乐会", "戏剧", "展览"]
        successful_generations = 0
        total_scenes = 0
        
        for scene_type in scene_types:
            scene = self._generate_scene(scene_type, 300)  # 5分钟演出
            
            if scene and "scenes" in scene:
                successful_generations += 1
                total_scenes += len(scene["scenes"])
                
                # 验证时间轴连续性
                scenes = scene["scenes"]
                time_continuity = True
                
                for i in range(len(scenes) - 1):
                    if abs(scenes[i]["end_time"] - scenes[i+1]["start_time"]) > 0.1:
                        time_continuity = False
                        break
                
                if not time_continuity:
                    successful_generations -= 0.5
        
        success_rate = successful_generations / len(scene_types)
        
        return {
            "success": success_rate >= 0.8,
            "message": f"场景生成成功率: {success_rate:.2%}, 总场景段落: {total_scenes}",
            "details": {
                "success_rate": success_rate,
                "total_scenes": total_scenes,
                "scene_types": len(scene_types)
            }
        }
    
    def test_timeline_calculation(self) -> Dict:
        """测试时间轴计算"""
        test_cases = [
            {"duration": 180, "stages": 4, "expected_ratios": [0.1, 0.3, 0.5, 0.1]},
            {"duration": 300, "stages": 5, "expected_ratios": [0.05, 0.25, 0.35, 0.25, 0.1]},
            {"duration": 240, "stages": 3, "expected_ratios": [0.33, 0.34, 0.33]}
        ]
        
        accurate_calculations = 0
        
        for case in test_cases:
            calculated_durations = self._calculate_stage_durations(
                case["duration"], case["stages"]
            )
            
            # 验证总时长
            total_calculated = sum(calculated_durations)
            if abs(total_calculated - case["duration"]) < 1.0:  # 允许1秒误差
                accurate_calculations += 1
        
        accuracy = accurate_calculations / len(test_cases)
        
        return {
            "success": accuracy >= 0.9,
            "message": f"时间轴计算准确率: {accuracy:.2%}",
            "details": {
                "accuracy": accuracy,
                "test_cases": len(test_cases)
            }
        }
    
    def test_sync_point_generation(self) -> Dict:
        """测试同步点生成"""
        beat_times = [i * 0.5 for i in range(100)]  # 50秒，100个节拍
        
        sync_points = self._generate_sync_points(beat_times)
        
        # 验证同步点
        expected_sync_count = len(beat_times) // 16  # 每16拍一个同步点
        actual_sync_count = len(sync_points)
        
        accuracy = min(actual_sync_count / expected_sync_count, 1.0) if expected_sync_count > 0 else 0
        
        return {
            "success": accuracy >= 0.8,
            "message": f"同步点生成准确率: {accuracy:.2%} ({actual_sync_count}/{expected_sync_count})",
            "details": {
                "accuracy": accuracy,
                "generated_points": actual_sync_count,
                "expected_points": expected_sync_count
            }
        }
    
    # 辅助方法
    def _classify_music_style(self, tempo: float, energy: float, brightness: float) -> str:
        """音乐风格分类算法"""
        if tempo < 70:
            return "慢板/抒情" if energy < 0.1 else "慢摇/蓝调"
        elif tempo < 100:
            return "中板/流行" if brightness > 2000 else "民谣/古典"
        elif tempo < 130:
            return "快板/摇滚" if energy > 0.15 else "爵士/放克"
        elif tempo < 160:
            return "舞曲/电子" if brightness > 3000 else "摇滚/金属"
        else:
            return "电子舞曲/鼓点" if energy > 0.2 else "快节奏流行"
    
    def _analyze_mood(self, energy: float, brightness: float) -> str:
        """情绪分析算法"""
        if energy > 0.15 and brightness > 2500:
            return "激昂/兴奋"
        elif energy < 0.08 and brightness < 1500:
            return "平静/忧郁"
        elif energy > 0.12:
            return "活跃/欢快"
        elif brightness < 2000:
            return "温和/舒缓"
        else:
            return "中性/平衡"
    
    def _generate_color_palette(self, mood: str) -> List[str]:
        """生成色彩方案"""
        palettes = {
            "激昂/兴奋": ["#FF0000", "#FF4500", "#FFD700", "#FFFFFF", "#FF69B4"],
            "平静/忧郁": ["#4169E1", "#6495ED", "#87CEEB", "#E6E6FA", "#D8BFD8"],
            "活跃/欢快": ["#FFD700", "#FFA500", "#FF6347", "#32CD32", "#00FF7F"],
            "温和/舒缓": ["#F5DEB3", "#DDA0DD", "#98FB98", "#FFEFD5", "#F0E68C"]
        }
        return palettes.get(mood, palettes["温和/舒缓"])
    
    def _is_valid_hex_color(self, color: str) -> bool:
        """验证十六进制颜色格式"""
        if not color.startswith("#") or len(color) != 7:
            return False
        try:
            int(color[1:], 16)
            return True
        except ValueError:
            return False
    
    def _generate_lighting_recommendation(self, music_data: Dict) -> Dict:
        """生成灯光推荐"""
        mood = music_data.get("mood", "中性/平衡")
        beat_times = music_data.get("beat_times", [])
        
        color_palette = self._generate_color_palette(mood)
        
        lighting_sequence = []
        for i, beat_time in enumerate(beat_times[:20]):
            lighting_sequence.append({
                "time": beat_time,
                "type": "primary_change" if i % 4 == 0 else "accent",
                "effect": random.choice(["渐变", "频闪", "追光"]),
                "color": random.choice(color_palette),
                "intensity": random.uniform(0.3, 1.0)
            })
        
        return {
            "color_palette": color_palette,
            "lighting_sequence": lighting_sequence,
            "confidence": random.uniform(0.7, 0.95)
        }
    
    def _generate_scene(self, scene_type: str, duration: float) -> Dict:
        """生成场景"""
        templates = {
            "音乐会": ["开场", "主题展示", "高潮", "尾声"],
            "戏剧": ["序幕", "第一幕", "第二幕", "第三幕", "尾声"],
            "展览": ["入场", "展示区1", "展示区2", "展示区3", "出场"]
        }
        
        stages = templates.get(scene_type, templates["音乐会"])
        durations = self._calculate_stage_durations(duration, len(stages))
        
        scenes = []
        current_time = 0
        
        for stage, stage_duration in zip(stages, durations):
            scenes.append({
                "stage_name": stage,
                "start_time": current_time,
                "duration": stage_duration,
                "end_time": current_time + stage_duration
            })
            current_time += stage_duration
        
        return {"scenes": scenes, "total_duration": duration}
    
    def _calculate_stage_durations(self, total_duration: float, stage_count: int) -> List[float]:
        """计算阶段时长"""
        if stage_count == 4:
            ratios = [0.1, 0.3, 0.5, 0.1]
        elif stage_count == 5:
            ratios = [0.05, 0.25, 0.35, 0.25, 0.1]
        else:
            ratios = [1.0 / stage_count] * stage_count
        
        return [total_duration * ratio for ratio in ratios]
    
    def _generate_sync_points(self, beat_times: List[float]) -> List[Dict]:
        """生成同步点"""
        sync_points = []
        for i in range(0, len(beat_times), 16):
            if i < len(beat_times):
                sync_points.append({
                    "time": beat_times[i],
                    "type": "major_sync"
                })
        return sync_points
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📊 测试报告")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"失败测试: {total_tests - passed_tests}")
        print(f"通过率: {passed_tests / total_tests:.2%}")
        
        print(f"\n⏱️ 总测试时间: {sum(r['duration'] for r in self.test_results):.3f}秒")
        
        # 保存详细报告
        report_data = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "pass_rate": passed_tests / total_tests,
                "total_duration": sum(r['duration'] for r in self.test_results)
            },
            "test_results": self.test_results,
            "generated_time": datetime.now().isoformat()
        }
        
        with open("ai_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: ai_test_report.json")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！AI系统功能正常")
        elif passed_tests / total_tests >= 0.8:
            print("\n✅ 大部分测试通过，系统基本功能正常")
        else:
            print("\n⚠️ 多个测试失败，需要检查系统功能")

if __name__ == "__main__":
    test_suite = AITestSuite()
    test_suite.run_all_tests()
