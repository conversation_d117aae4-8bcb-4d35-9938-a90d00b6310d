#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MA控台专业AI灯光系统
支持MA XML灯具库 + AI音乐分析 + Art-Net输出
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import socket
import struct
import threading
import time
import random
from datetime import datetime
from typing import Dict, List, Optional
from ma_xml_fixture_parser import MAFixtureParser

class MALightingSystem:
    """MA控台专业AI灯光系统"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 MA控台专业AI灯光系统")
        self.root.geometry("1600x1000")
        self.root.configure(bg="#1a1a1a")

        # 初始化组件
        self.ma_parser = MAFixtureParser()
        self.configured_fixtures = []
        self.current_music_analysis = None
        self.current_sequence = []
        self.artnet_socket = None
        self.is_playing = False
        self.playback_thread = None

        # 数据存储
        self.universe_data = {}  # 存储每个Universe的DMX数据
        self.sequence_index = 0
        self.start_time = 0

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主标题栏
        title_frame = tk.Frame(self.root, bg="#0f172a", height=80)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)

        # 标题
        title_label = tk.Label(title_frame, text="🎭 MA控台专业AI灯光系统",
                              font=("Arial", 22, "bold"), fg="#f8fafc", bg="#0f172a")
        title_label.pack(pady=10)

        subtitle_label = tk.Label(title_frame, text="MA XML灯具库 → 音乐AI分析 → Art-Net输出 → GrandMA2/MA3控台",
                                 font=("Arial", 12), fg="#94a3b8", bg="#0f172a")
        subtitle_label.pack()

        # 主要内容区域
        main_frame = tk.Frame(self.root, bg="#1e293b")
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建选项卡
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background='#1e293b')
        style.configure('TNotebook.Tab', background='#334155', foreground='white', padding=[20, 10])
        style.map('TNotebook.Tab', background=[('selected', '#0ea5e9')])

        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)

        # 1. MA灯具库管理
        self.create_fixture_library_tab()

        # 2. 音乐分析
        self.create_music_analysis_tab()

        # 3. 灯具配接
        self.create_fixture_patching_tab()

        # 4. AI序列生成
        self.create_ai_sequence_tab()

        # 5. 实时控制
        self.create_realtime_control_tab()

        # 状态栏
        self.create_status_bar()

    def create_fixture_library_tab(self):
        """创建MA灯具库管理选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="📚 MA灯具库")

        # 灯具库导入区域
        import_frame = tk.LabelFrame(frame, text="MA XML灯具库导入",
                                    font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        import_frame.pack(fill="x", padx=10, pady=5)

        # 文件选择
        file_frame = tk.Frame(import_frame, bg="#1e293b")
        file_frame.pack(fill="x", padx=10, pady=5)

        self.xml_file_var = tk.StringVar()
        xml_entry = tk.Entry(file_frame, textvariable=self.xml_file_var, width=80,
                            font=("Arial", 10), bg="#334155", fg="white")
        xml_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))

        browse_xml_btn = tk.Button(file_frame, text="📁 选择MA XML文件",
                                  command=self.browse_ma_xml_file,
                                  bg="#0ea5e9", fg="white", font=("Arial", 10, "bold"))
        browse_xml_btn.pack(side="right", padx=5)

        parse_btn = tk.Button(file_frame, text="🔍 解析灯具库",
                             command=self.parse_ma_xml,
                             bg="#059669", fg="white", font=("Arial", 10, "bold"))
        parse_btn.pack(side="right", padx=5)

        # 灯具库显示区域
        library_frame = tk.LabelFrame(frame, text="已加载的灯具库",
                                     font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        library_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 搜索框
        search_frame = tk.Frame(library_frame, bg="#1e293b")
        search_frame.pack(fill="x", padx=5, pady=5)

        tk.Label(search_frame, text="搜索:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30,
                               font=("Arial", 10), bg="#334155", fg="white")
        search_entry.pack(side="left", padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)

        # 制造商筛选
        tk.Label(search_frame, text="制造商:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.manufacturer_var = tk.StringVar(value="全部")
        self.manufacturer_combo = ttk.Combobox(search_frame, textvariable=self.manufacturer_var,
                                              width=20, state="readonly")
        self.manufacturer_combo.pack(side="left", padx=5)
        self.manufacturer_combo.bind('<<ComboboxSelected>>', self.on_manufacturer_change)

        # 灯具列表
        list_frame = tk.Frame(library_frame, bg="#1e293b")
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建表格
        columns = ("制造商", "灯具名称", "通道数", "模式", "功率", "类型")
        self.fixture_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=20)

        # 设置列标题和宽度
        column_widths = {"制造商": 120, "灯具名称": 200, "通道数": 80, "模式": 100, "功率": 80, "类型": 120}
        for col in columns:
            self.fixture_tree.heading(col, text=col)
            self.fixture_tree.column(col, width=column_widths.get(col, 100))

        # 滚动条
        fixture_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=self.fixture_tree.yview)
        self.fixture_tree.configure(yscrollcommand=fixture_scroll.set)

        self.fixture_tree.pack(side="left", fill="both", expand=True)
        fixture_scroll.pack(side="right", fill="y")

        # 双击查看详情
        self.fixture_tree.bind('<Double-1>', self.show_fixture_details)

        # 统计信息
        stats_frame = tk.Frame(library_frame, bg="#1e293b")
        stats_frame.pack(fill="x", padx=5, pady=5)

        self.stats_label = tk.Label(stats_frame, text="灯具库统计: 未加载",
                                   font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.stats_label.pack(side="left")

        # 导出按钮
        export_btn = tk.Button(stats_frame, text="💾 导出为JSON",
                              command=self.export_fixtures_json,
                              bg="#7c3aed", fg="white", font=("Arial", 10))
        export_btn.pack(side="right", padx=5)

    def create_music_analysis_tab(self):
        """创建音乐分析选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🎵 音乐分析")

        # 音乐文件选择
        music_frame = tk.LabelFrame(frame, text="音乐文件分析",
                                   font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        music_frame.pack(fill="x", padx=10, pady=5)

        file_frame = tk.Frame(music_frame, bg="#1e293b")
        file_frame.pack(fill="x", padx=10, pady=5)

        self.music_file_var = tk.StringVar()
        music_entry = tk.Entry(file_frame, textvariable=self.music_file_var, width=80,
                              font=("Arial", 10), bg="#334155", fg="white")
        music_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))

        browse_music_btn = tk.Button(file_frame, text="📁 选择音乐文件",
                                    command=self.browse_music_file,
                                    bg="#0ea5e9", fg="white", font=("Arial", 10, "bold"))
        browse_music_btn.pack(side="right", padx=5)

        analyze_btn = tk.Button(file_frame, text="🤖 AI分析",
                               command=self.analyze_music,
                               bg="#dc2626", fg="white", font=("Arial", 10, "bold"))
        analyze_btn.pack(side="right", padx=5)

        # 分析结果显示
        results_frame = tk.LabelFrame(frame, text="AI分析结果",
                                     font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 左侧：基本信息
        info_frame = tk.Frame(results_frame, bg="#1e293b")
        info_frame.pack(side="left", fill="y", padx=10, pady=10)

        tk.Label(info_frame, text="音乐特征", font=("Arial", 14, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))

        self.bpm_label = tk.Label(info_frame, text="BPM: --", font=("Arial", 12),
                                 fg="#94a3b8", bg="#1e293b")
        self.bpm_label.pack(anchor="w", pady=2)

        self.duration_label = tk.Label(info_frame, text="时长: --", font=("Arial", 12),
                                      fg="#94a3b8", bg="#1e293b")
        self.duration_label.pack(anchor="w", pady=2)

        self.style_label = tk.Label(info_frame, text="风格: --", font=("Arial", 12),
                                   fg="#94a3b8", bg="#1e293b")
        self.style_label.pack(anchor="w", pady=2)

        self.mood_label = tk.Label(info_frame, text="情绪: --", font=("Arial", 12),
                                  fg="#94a3b8", bg="#1e293b")
        self.mood_label.pack(anchor="w", pady=2)

        self.beats_label = tk.Label(info_frame, text="节拍数: --", font=("Arial", 12),
                                   fg="#94a3b8", bg="#1e293b")
        self.beats_label.pack(anchor="w", pady=2)

        # 右侧：详细分析
        detail_frame = tk.Frame(results_frame, bg="#1e293b")
        detail_frame.pack(side="right", fill="both", expand=True, padx=10, pady=10)

        tk.Label(detail_frame, text="详细分析数据", font=("Arial", 14, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))

        self.analysis_text = tk.Text(detail_frame, height=20, font=("Consolas", 9),
                                    bg="#334155", fg="white", insertbackground="white")
        analysis_scroll = ttk.Scrollbar(detail_frame, orient="vertical", command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scroll.set)

        self.analysis_text.pack(side="left", fill="both", expand=True)
        analysis_scroll.pack(side="right", fill="y")

    def create_fixture_patching_tab(self):
        """创建灯具配接选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🔌 灯具配接")

        # 配接控制区域
        patch_frame = tk.LabelFrame(frame, text="灯具配接设置",
                                   font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        patch_frame.pack(fill="x", padx=10, pady=5)

        # 配接参数
        param_frame = tk.Frame(patch_frame, bg="#1e293b")
        param_frame.pack(fill="x", padx=10, pady=10)

        # 第一行参数
        row1 = tk.Frame(param_frame, bg="#1e293b")
        row1.pack(fill="x", pady=5)

        tk.Label(row1, text="灯具类型:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.selected_fixture_var = tk.StringVar()
        self.fixture_combo = ttk.Combobox(row1, textvariable=self.selected_fixture_var,
                                         width=30, state="readonly")
        self.fixture_combo.pack(side="left", padx=5)

        tk.Label(row1, text="起始地址:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.start_address_var = tk.StringVar(value="1")
        tk.Entry(row1, textvariable=self.start_address_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        tk.Label(row1, text="Universe:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.universe_var = tk.StringVar(value="1")
        tk.Entry(row1, textvariable=self.universe_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        # 第二行参数
        row2 = tk.Frame(param_frame, bg="#1e293b")
        row2.pack(fill="x", pady=5)

        tk.Label(row2, text="灯具名称:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.fixture_name_var = tk.StringVar()
        tk.Entry(row2, textvariable=self.fixture_name_var, width=25,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        tk.Label(row2, text="区域:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.zone_var = tk.StringVar(value="舞台前区")
        zone_combo = ttk.Combobox(row2, textvariable=self.zone_var,
                                 values=["舞台前区", "舞台后区", "观众席", "背景区", "特效区", "侧光区"],
                                 width=15, state="readonly")
        zone_combo.pack(side="left", padx=5)

        tk.Label(row2, text="模式:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.mode_var = tk.StringVar()
        self.mode_combo = ttk.Combobox(row2, textvariable=self.mode_var,
                                      width=15, state="readonly")
        self.mode_combo.pack(side="left", padx=5)

        # 配接按钮
        add_fixture_btn = tk.Button(param_frame, text="➕ 添加灯具",
                                   command=self.add_fixture_patch,
                                   bg="#059669", fg="white", font=("Arial", 12, "bold"))
        add_fixture_btn.pack(pady=10)

        # 已配接灯具列表
        patched_frame = tk.LabelFrame(frame, text="已配接灯具",
                                     font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        patched_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建配接表格
        patch_columns = ("ID", "名称", "类型", "制造商", "地址", "Universe", "区域", "模式", "通道数")
        self.patch_tree = ttk.Treeview(patched_frame, columns=patch_columns, show="headings", height=15)

        # 设置列
        patch_widths = {"ID": 50, "名称": 150, "类型": 150, "制造商": 100, "地址": 60,
                       "Universe": 70, "区域": 100, "模式": 80, "通道数": 70}
        for col in patch_columns:
            self.patch_tree.heading(col, text=col)
            self.patch_tree.column(col, width=patch_widths.get(col, 80))

        patch_scroll = ttk.Scrollbar(patched_frame, orient="vertical", command=self.patch_tree.yview)
        self.patch_tree.configure(yscrollcommand=patch_scroll.set)

        self.patch_tree.pack(side="left", fill="both", expand=True)
        patch_scroll.pack(side="right", fill="y")

        # 右键菜单
        self.patch_tree.bind('<Button-3>', self.show_patch_context_menu)

        # 配接统计
        patch_stats_frame = tk.Frame(patched_frame, bg="#1e293b")
        patch_stats_frame.pack(fill="x", padx=5, pady=5)

        self.patch_stats_label = tk.Label(patch_stats_frame, text="配接统计: 0个灯具",
                                         font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.patch_stats_label.pack(side="left")

        # 批量操作按钮
        batch_frame = tk.Frame(patch_stats_frame, bg="#1e293b")
        batch_frame.pack(side="right")

        tk.Button(batch_frame, text="🗑️ 清空", command=self.clear_all_patches,
                 bg="#dc2626", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        tk.Button(batch_frame, text="💾 保存配接", command=self.save_patch_config,
                 bg="#7c3aed", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        tk.Button(batch_frame, text="📁 加载配接", command=self.load_patch_config,
                 bg="#0ea5e9", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

    def create_ai_sequence_tab(self):
        """创建AI序列生成选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🤖 AI序列")

        # AI生成控制
        ai_frame = tk.LabelFrame(frame, text="AI灯光序列生成",
                                font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        ai_frame.pack(fill="x", padx=10, pady=5)

        control_frame = tk.Frame(ai_frame, bg="#1e293b")
        control_frame.pack(fill="x", padx=10, pady=10)

        # AI参数设置
        param_frame = tk.Frame(control_frame, bg="#1e293b")
        param_frame.pack(side="left", fill="x", expand=True)

        tk.Label(param_frame, text="AI生成参数", font=("Arial", 12, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 5))

        # 参数行1
        param_row1 = tk.Frame(param_frame, bg="#1e293b")
        param_row1.pack(fill="x", pady=2)

        tk.Label(param_row1, text="创意级别:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.creativity_var = tk.StringVar(value="中等")
        creativity_combo = ttk.Combobox(param_row1, textvariable=self.creativity_var,
                                       values=["保守", "中等", "创新", "极致"], width=10, state="readonly")
        creativity_combo.pack(side="left", padx=5)

        tk.Label(param_row1, text="同步精度:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.sync_precision_var = tk.StringVar(value="高")
        sync_combo = ttk.Combobox(param_row1, textvariable=self.sync_precision_var,
                                 values=["低", "中", "高", "极高"], width=10, state="readonly")
        sync_combo.pack(side="left", padx=5)

        # 参数行2
        param_row2 = tk.Frame(param_frame, bg="#1e293b")
        param_row2.pack(fill="x", pady=2)

        tk.Label(param_row2, text="效果强度:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.intensity_scale = tk.Scale(param_row2, from_=0.1, to=1.0, resolution=0.1,
                                       orient="horizontal", length=150, bg="#334155", fg="white")
        self.intensity_scale.set(0.8)
        self.intensity_scale.pack(side="left", padx=5)

        tk.Label(param_row2, text="色彩丰富度:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.color_richness_scale = tk.Scale(param_row2, from_=0.1, to=1.0, resolution=0.1,
                                            orient="horizontal", length=150, bg="#334155", fg="white")
        self.color_richness_scale.set(0.7)
        self.color_richness_scale.pack(side="left", padx=5)

        # 生成按钮
        generate_frame = tk.Frame(control_frame, bg="#1e293b")
        generate_frame.pack(side="right", padx=20)

        generate_btn = tk.Button(generate_frame, text="🤖 生成AI序列",
                                command=self.generate_ai_sequence,
                                bg="#7c3aed", fg="white", font=("Arial", 14, "bold"),
                                width=15, height=2)
        generate_btn.pack(pady=10)

        # 进度显示
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(generate_frame, variable=self.progress_var,
                                           length=200, mode='determinate')
        self.progress_bar.pack(pady=5)

        # 序列显示区域
        sequence_frame = tk.LabelFrame(frame, text="生成的灯光序列",
                                      font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        sequence_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 序列统计
        seq_stats_frame = tk.Frame(sequence_frame, bg="#1e293b")
        seq_stats_frame.pack(fill="x", padx=5, pady=5)

        self.sequence_stats_label = tk.Label(seq_stats_frame, text="序列统计: 未生成",
                                            font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.sequence_stats_label.pack(side="left")

        # 序列操作按钮
        seq_ops_frame = tk.Frame(seq_stats_frame, bg="#1e293b")
        seq_ops_frame.pack(side="right")

        tk.Button(seq_ops_frame, text="👁️ 预览", command=self.preview_sequence,
                 bg="#0ea5e9", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        tk.Button(seq_ops_frame, text="💾 导出", command=self.export_sequence,
                 bg="#059669", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        tk.Button(seq_ops_frame, text="🔄 重新生成", command=self.regenerate_sequence,
                 bg="#dc2626", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        # 序列表格
        seq_columns = ("时间", "灯具", "区域", "通道", "值", "效果", "颜色")
        self.sequence_tree = ttk.Treeview(sequence_frame, columns=seq_columns, show="headings", height=18)

        seq_widths = {"时间": 80, "灯具": 150, "区域": 100, "通道": 80, "值": 60, "效果": 100, "颜色": 100}
        for col in seq_columns:
            self.sequence_tree.heading(col, text=col)
            self.sequence_tree.column(col, width=seq_widths.get(col, 80))

        seq_scroll = ttk.Scrollbar(sequence_frame, orient="vertical", command=self.sequence_tree.yview)
        self.sequence_tree.configure(yscrollcommand=seq_scroll.set)

        self.sequence_tree.pack(side="left", fill="both", expand=True)
        seq_scroll.pack(side="right", fill="y")

    def create_realtime_control_tab(self):
        """创建实时控制选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🎮 实时控制")

        # Art-Net连接设置
        artnet_frame = tk.LabelFrame(frame, text="Art-Net连接设置",
                                    font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        artnet_frame.pack(fill="x", padx=10, pady=5)

        conn_frame = tk.Frame(artnet_frame, bg="#1e293b")
        conn_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(conn_frame, text="目标IP:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.artnet_ip_var = tk.StringVar(value="*********")  # MA控台常用IP
        tk.Entry(conn_frame, textvariable=self.artnet_ip_var, width=15,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        tk.Label(conn_frame, text="端口:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.artnet_port_var = tk.StringVar(value="6454")
        tk.Entry(conn_frame, textvariable=self.artnet_port_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        self.connect_btn = tk.Button(conn_frame, text="🔗 连接Art-Net",
                                    command=self.connect_artnet,
                                    bg="#059669", fg="white", font=("Arial", 10, "bold"))
        self.connect_btn.pack(side="left", padx=20)

        self.disconnect_btn = tk.Button(conn_frame, text="🔌 断开连接",
                                       command=self.disconnect_artnet,
                                       bg="#dc2626", fg="white", font=("Arial", 10, "bold"))
        self.disconnect_btn.pack(side="left", padx=5)

        # 连接状态
        self.connection_status_label = tk.Label(conn_frame, text="🔴 未连接",
                                               font=("Arial", 12, "bold"), fg="#ef4444", bg="#1e293b")
        self.connection_status_label.pack(side="right", padx=20)

        # 播放控制
        playback_frame = tk.LabelFrame(frame, text="播放控制",
                                      font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        playback_frame.pack(fill="x", padx=10, pady=5)

        play_frame = tk.Frame(playback_frame, bg="#1e293b")
        play_frame.pack(fill="x", padx=10, pady=10)

        # 播放按钮
        self.play_btn = tk.Button(play_frame, text="▶️ 播放",
                                 command=self.start_playback,
                                 bg="#059669", fg="white", font=("Arial", 14, "bold"),
                                 width=8, height=2)
        self.play_btn.pack(side="left", padx=5)

        self.pause_btn = tk.Button(play_frame, text="⏸️ 暂停",
                                  command=self.pause_playback,
                                  bg="#f59e0b", fg="white", font=("Arial", 14, "bold"),
                                  width=8, height=2)
        self.pause_btn.pack(side="left", padx=5)

        self.stop_btn = tk.Button(play_frame, text="⏹️ 停止",
                                 command=self.stop_playback,
                                 bg="#dc2626", fg="white", font=("Arial", 14, "bold"),
                                 width=8, height=2)
        self.stop_btn.pack(side="left", padx=5)

        # 时间显示和进度条
        time_frame = tk.Frame(play_frame, bg="#1e293b")
        time_frame.pack(side="right", fill="x", expand=True, padx=20)

        self.time_label = tk.Label(time_frame, text="00:00 / 00:00",
                                  font=("Arial", 16, "bold"), fg="white", bg="#1e293b")
        self.time_label.pack(pady=5)

        self.playback_progress_var = tk.DoubleVar()
        self.playback_progress = ttk.Scale(time_frame, from_=0, to=100, orient="horizontal",
                                          variable=self.playback_progress_var, length=400)
        self.playback_progress.pack(fill="x", pady=5)

        # 实时状态显示
        status_frame = tk.LabelFrame(frame, text="实时状态",
                                    font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        status_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 左侧：系统状态
        left_status = tk.Frame(status_frame, bg="#1e293b")
        left_status.pack(side="left", fill="y", padx=10, pady=10)

        tk.Label(left_status, text="系统状态", font=("Arial", 12, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))

        self.current_time_label = tk.Label(left_status, text="播放时间: --",
                                          font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.current_time_label.pack(anchor="w", pady=2)

        self.current_bpm_label = tk.Label(left_status, text="当前BPM: --",
                                         font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.current_bpm_label.pack(anchor="w", pady=2)

        self.active_fixtures_label = tk.Label(left_status, text="活跃灯具: 0",
                                             font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.active_fixtures_label.pack(anchor="w", pady=2)

        self.sent_packets_label = tk.Label(left_status, text="发送包数: 0",
                                          font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.sent_packets_label.pack(anchor="w", pady=2)

        # 右侧：活跃灯具列表
        right_status = tk.Frame(status_frame, bg="#1e293b")
        right_status.pack(side="right", fill="both", expand=True, padx=10, pady=10)

        tk.Label(right_status, text="活跃灯具状态", font=("Arial", 12, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))

        # 活跃灯具表格
        active_columns = ("灯具", "Universe", "地址", "强度", "颜色", "状态")
        self.active_tree = ttk.Treeview(right_status, columns=active_columns, show="headings", height=12)

        active_widths = {"灯具": 120, "Universe": 70, "地址": 60, "强度": 60, "颜色": 80, "状态": 80}
        for col in active_columns:
            self.active_tree.heading(col, text=col)
            self.active_tree.column(col, width=active_widths.get(col, 70))

        active_scroll = ttk.Scrollbar(right_status, orient="vertical", command=self.active_tree.yview)
        self.active_tree.configure(yscrollcommand=active_scroll.set)

        self.active_tree.pack(side="left", fill="both", expand=True)
        active_scroll.pack(side="right", fill="y")

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, relief="sunken", bd=1, bg="#0f172a")
        status_frame.pack(side="bottom", fill="x")

        self.status_label = tk.Label(status_frame, text="就绪 - 请导入MA XML灯具库开始",
                                    anchor="w", bg="#0f172a", fg="#94a3b8", font=("Arial", 10))
        self.status_label.pack(side="left", padx=10, pady=5)

        # 版本信息
        version_label = tk.Label(status_frame, text="MA AI Lighting System v1.0",
                                anchor="e", bg="#0f172a", fg="#64748b", font=("Arial", 9))
        version_label.pack(side="right", padx=10, pady=5)

    # ==================== 核心功能方法 ====================

    def browse_ma_xml_file(self):
        """浏览MA XML文件"""
        file_types = [
            ("MA XML文件", "*.xml"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择MA控台XML灯具库文件",
            filetypes=file_types
        )

        if file_path:
            self.xml_file_var.set(file_path)
            self.status_label.config(text=f"已选择MA XML文件: {file_path}")

    def parse_ma_xml(self):
        """解析MA XML灯具库"""
        xml_file = self.xml_file_var.get()
        if not xml_file:
            messagebox.showwarning("警告", "请先选择MA XML文件")
            return

        try:
            self.status_label.config(text="正在解析MA XML灯具库...")

            # 解析XML文件
            if self.ma_parser.parse_ma_xml_library(xml_file):
                self.update_fixture_library_display()
                self.update_fixture_combo()

                stats = self.ma_parser.get_statistics()
                self.status_label.config(text=f"成功解析 {stats['total_fixtures']} 个灯具类型")

                messagebox.showinfo("成功",
                                   f"成功解析MA XML灯具库！\n\n"
                                   f"总灯具数: {stats['total_fixtures']}\n"
                                   f"制造商数: {stats['manufacturer_count']}\n"
                                   f"制造商: {', '.join(stats['manufacturers'][:5])}{'...' if len(stats['manufacturers']) > 5 else ''}")
            else:
                messagebox.showerror("错误", "MA XML文件解析失败")

        except Exception as e:
            messagebox.showerror("错误", f"解析过程出错: {e}")

    def update_fixture_library_display(self):
        """更新灯具库显示"""
        # 清除现有数据
        for item in self.fixture_tree.get_children():
            self.fixture_tree.delete(item)

        # 添加灯具数据
        for fixture_id, fixture in self.ma_parser.fixtures.items():
            # 获取主要模式信息
            modes = list(fixture.get('modes', {}).keys())
            main_mode = modes[0] if modes else "默认"

            # 获取功率信息
            power = fixture.get('physical', {}).get('power', 'N/A')
            power_str = f"{power}W" if isinstance(power, (int, float)) else str(power)

            # 确定灯具类型
            fixture_type = self._determine_fixture_type(fixture)

            self.fixture_tree.insert("", "end", values=(
                fixture['manufacturer'],
                fixture['name'],
                fixture['channel_count'],
                main_mode,
                power_str,
                fixture_type
            ))

        # 更新统计信息
        stats = self.ma_parser.get_statistics()
        self.stats_label.config(text=f"灯具库统计: {stats['total_fixtures']}个灯具, {stats['manufacturer_count']}个制造商")

        # 更新制造商下拉框
        manufacturers = ["全部"] + stats['manufacturers']
        self.manufacturer_combo['values'] = manufacturers

    def _determine_fixture_type(self, fixture):
        """确定灯具类型"""
        name = fixture['name'].lower()
        channels = fixture.get('channels', {})

        if 'pan' in channels and 'tilt' in channels:
            if 'gobo' in channels or 'prism' in channels:
                return "摇头聚光灯"
            else:
                return "摇头染色灯"
        elif any(color in channels for color in ['red', 'green', 'blue']):
            return "LED灯具"
        elif 'dimmer' in channels:
            return "调光灯具"
        else:
            return "其他"

    def on_search_change(self, event=None):
        """搜索变化事件"""
        self.filter_fixtures()

    def on_manufacturer_change(self, event=None):
        """制造商筛选变化事件"""
        self.filter_fixtures()

    def filter_fixtures(self):
        """筛选灯具"""
        search_text = self.search_var.get().lower()
        selected_manufacturer = self.manufacturer_var.get()

        # 清除现有显示
        for item in self.fixture_tree.get_children():
            self.fixture_tree.delete(item)

        # 筛选并显示
        filtered_count = 0
        for fixture_id, fixture in self.ma_parser.fixtures.items():
            # 制造商筛选
            if selected_manufacturer != "全部" and fixture['manufacturer'] != selected_manufacturer:
                continue

            # 文本搜索
            if search_text and search_text not in fixture['name'].lower() and search_text not in fixture['manufacturer'].lower():
                continue

            # 显示符合条件的灯具
            modes = list(fixture.get('modes', {}).keys())
            main_mode = modes[0] if modes else "默认"

            power = fixture.get('physical', {}).get('power', 'N/A')
            power_str = f"{power}W" if isinstance(power, (int, float)) else str(power)

            fixture_type = self._determine_fixture_type(fixture)

            self.fixture_tree.insert("", "end", values=(
                fixture['manufacturer'],
                fixture['name'],
                fixture['channel_count'],
                main_mode,
                power_str,
                fixture_type
            ))

            filtered_count += 1

        # 更新统计
        total_count = len(self.ma_parser.fixtures)
        self.stats_label.config(text=f"显示: {filtered_count}/{total_count}个灯具")

    def show_fixture_details(self, event):
        """显示灯具详情"""
        selection = self.fixture_tree.selection()
        if not selection:
            return

        item = self.fixture_tree.item(selection[0])
        manufacturer = item['values'][0]
        fixture_name = item['values'][1]

        # 查找对应的灯具
        fixture = None
        for f in self.ma_parser.fixtures.values():
            if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                fixture = f
                break

        if fixture:
            self._show_fixture_detail_window(fixture)

    def _show_fixture_detail_window(self, fixture):
        """显示灯具详情窗口"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"灯具详情 - {fixture['name']}")
        detail_window.geometry("600x500")
        detail_window.configure(bg="#1e293b")

        # 基本信息
        info_frame = tk.LabelFrame(detail_window, text="基本信息",
                                  font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        info_frame.pack(fill="x", padx=10, pady=5)

        info_text = f"""制造商: {fixture['manufacturer']}
灯具名称: {fixture['name']}
通道数: {fixture['channel_count']}
来源: {fixture.get('source', 'Unknown')}"""

        tk.Label(info_frame, text=info_text, font=("Arial", 10),
                fg="white", bg="#1e293b", justify="left").pack(anchor="w", padx=10, pady=10)

        # 通道信息
        channels_frame = tk.LabelFrame(detail_window, text="通道定义",
                                      font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        channels_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建通道表格
        ch_columns = ("通道", "功能", "范围", "默认值")
        ch_tree = ttk.Treeview(channels_frame, columns=ch_columns, show="headings", height=15)

        for col in ch_columns:
            ch_tree.heading(col, text=col)
            ch_tree.column(col, width=120)

        # 添加通道数据
        for ch_name, ch_info in fixture.get('channels', {}).items():
            ch_tree.insert("", "end", values=(
                ch_name,
                ch_info.get('function', 'Unknown'),
                f"{ch_info.get('range', [0, 255])[0]}-{ch_info.get('range', [0, 255])[1]}",
                ch_info.get('default', 0)
            ))

        ch_scroll = ttk.Scrollbar(channels_frame, orient="vertical", command=ch_tree.yview)
        ch_tree.configure(yscrollcommand=ch_scroll.set)

        ch_tree.pack(side="left", fill="both", expand=True)
        ch_scroll.pack(side="right", fill="y")

    def export_fixtures_json(self):
        """导出灯具库为JSON"""
        if not self.ma_parser.fixtures:
            messagebox.showwarning("警告", "没有可导出的灯具库")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出灯具库",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if self.ma_parser.export_to_json(file_path):
                messagebox.showinfo("成功", f"灯具库已导出到: {file_path}")
            else:
                messagebox.showerror("错误", "导出失败")

    def update_fixture_combo(self):
        """更新灯具选择下拉框"""
        fixture_names = []
        for fixture in self.ma_parser.fixtures.values():
            display_name = f"{fixture['manufacturer']} - {fixture['name']}"
            fixture_names.append(display_name)

        self.fixture_combo['values'] = fixture_names

        # 绑定选择事件
        self.fixture_combo.bind('<<ComboboxSelected>>', self.on_fixture_selected)

    def on_fixture_selected(self, event=None):
        """灯具选择事件"""
        selected = self.selected_fixture_var.get()
        if not selected:
            return

        # 解析选择的灯具
        try:
            manufacturer, fixture_name = selected.split(" - ", 1)

            # 查找对应的灯具
            fixture = None
            for f in self.ma_parser.fixtures.values():
                if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                    fixture = f
                    break

            if fixture:
                # 更新模式下拉框
                modes = list(fixture.get('modes', {}).keys())
                self.mode_combo['values'] = modes
                if modes:
                    self.mode_var.set(modes[0])

                # 自动填充灯具名称
                if not self.fixture_name_var.get():
                    self.fixture_name_var.set(f"{fixture_name}_{len(self.configured_fixtures)+1}")

        except ValueError:
            pass

    def browse_music_file(self):
        """浏览音乐文件"""
        file_types = [
            ("音频文件", "*.mp3 *.wav *.flac *.m4a *.aac"),
            ("MP3文件", "*.mp3"),
            ("WAV文件", "*.wav"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择音乐文件",
            filetypes=file_types
        )

        if file_path:
            self.music_file_var.set(file_path)
            self.status_label.config(text=f"已选择音乐文件: {file_path}")

    def analyze_music(self):
        """分析音乐"""
        music_file = self.music_file_var.get()
        if not music_file:
            messagebox.showwarning("警告", "请先选择音乐文件")
            return

        # 在新线程中执行分析
        analysis_thread = threading.Thread(target=self._analyze_music_thread, args=(music_file,), daemon=True)
        analysis_thread.start()

    def _analyze_music_thread(self, music_file):
        """音乐分析线程"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="AI正在分析音乐..."))

            # 模拟AI音乐分析过程
            time.sleep(2)  # 模拟处理时间

            # 生成分析结果
            bpm = random.uniform(80, 160)
            duration = random.uniform(180, 300)

            # 生成节拍时间点
            beat_interval = 60 / bpm
            beat_times = []
            current_time = 0
            while current_time < duration:
                beat_times.append(current_time)
                current_time += beat_interval

            # 生成能量分布
            energy_profile = []
            for i in range(int(duration / 10)):  # 每10秒一个能量值
                base_energy = random.uniform(0.3, 0.8)
                # 添加一些变化
                if i > len(beat_times) * 0.3 and i < len(beat_times) * 0.7:  # 中间部分能量较高
                    base_energy += random.uniform(0.1, 0.3)
                energy_profile.append(min(1.0, base_energy))

            # 分析音乐特征
            avg_energy = sum(energy_profile) / len(energy_profile)

            # 风格分类
            if bpm < 90:
                style = "慢板/抒情"
            elif bpm < 120:
                style = "中板/流行"
            elif bpm < 140:
                style = "快板/摇滚"
            else:
                style = "电子/舞曲"

            # 情绪分析
            if avg_energy > 0.7:
                mood = "激昂/兴奋"
            elif avg_energy < 0.4:
                mood = "平静/舒缓"
            elif bpm > 130:
                mood = "动感/活跃"
            else:
                mood = "稳定/平衡"

            self.current_music_analysis = {
                'file_path': music_file,
                'bpm': bpm,
                'duration': duration,
                'beat_times': beat_times,
                'energy_profile': energy_profile,
                'style': style,
                'mood': mood,
                'avg_energy': avg_energy,
                'analysis_time': datetime.now().isoformat()
            }

            # 更新界面显示
            self.root.after(0, self.update_music_analysis_display)
            self.root.after(0, lambda: self.status_label.config(text="音乐分析完成"))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"音乐分析失败: {e}"))

    def update_music_analysis_display(self):
        """更新音乐分析显示"""
        if not self.current_music_analysis:
            return

        analysis = self.current_music_analysis

        # 更新基本信息标签
        self.bpm_label.config(text=f"BPM: {analysis['bpm']:.1f}")
        self.duration_label.config(text=f"时长: {analysis['duration']:.1f}秒")
        self.style_label.config(text=f"风格: {analysis['style']}")
        self.mood_label.config(text=f"情绪: {analysis['mood']}")
        self.beats_label.config(text=f"节拍数: {len(analysis['beat_times'])}")

        # 更新详细分析文本
        detail_text = f"""🎵 AI音乐分析详细报告
{'='*50}

文件信息:
  路径: {analysis['file_path']}
  时长: {analysis['duration']:.1f} 秒

音乐特征:
  BPM: {analysis['bpm']:.1f}
  风格: {analysis['style']}
  情绪: {analysis['mood']}
  平均能量: {analysis['avg_energy']:.2f}

节拍分析:
  总节拍数: {len(analysis['beat_times'])}
  节拍间隔: {60/analysis['bpm']:.3f} 秒

前20个节拍时间点:
"""

        for i, beat_time in enumerate(analysis['beat_times'][:20]):
            detail_text += f"  节拍 {i+1:2d}: {beat_time:6.2f}s\n"

        if len(analysis['beat_times']) > 20:
            detail_text += f"  ... 还有 {len(analysis['beat_times']) - 20} 个节拍\n"

        detail_text += f"\n能量分布 (每10秒):\n"
        for i, energy in enumerate(analysis['energy_profile']):
            detail_text += f"  {i*10:3d}s-{(i+1)*10:3d}s: {'█' * int(energy * 20)} {energy:.2f}\n"

        detail_text += f"\n分析完成时间: {analysis['analysis_time']}"

        self.analysis_text.delete("1.0", "end")
        self.analysis_text.insert("1.0", detail_text)

    def add_fixture_patch(self):
        """添加灯具配接"""
        selected_fixture = self.selected_fixture_var.get()
        if not selected_fixture:
            messagebox.showwarning("警告", "请先选择灯具类型")
            return

        try:
            # 解析选择的灯具
            manufacturer, fixture_name = selected_fixture.split(" - ", 1)

            # 查找灯具定义
            fixture_def = None
            for f in self.ma_parser.fixtures.values():
                if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                    fixture_def = f
                    break

            if not fixture_def:
                messagebox.showerror("错误", "找不到灯具定义")
                return

            # 获取配接参数
            start_address = int(self.start_address_var.get())
            universe = int(self.universe_var.get())
            zone = self.zone_var.get()
            mode = self.mode_var.get()
            name = self.fixture_name_var.get() or f"{fixture_name}_{len(self.configured_fixtures)+1}"

            # 验证地址范围
            mode_info = fixture_def.get('modes', {}).get(mode, {})
            channel_count = mode_info.get('channel_count', fixture_def['channel_count'])

            if start_address + channel_count - 1 > 512:
                messagebox.showerror("错误", f"地址范围超出Universe限制 (需要{channel_count}个通道)")
                return

            # 检查地址冲突
            for existing in self.configured_fixtures:
                if (existing['universe'] == universe and
                    existing['start_address'] <= start_address < existing['start_address'] + existing['channel_count']):
                    messagebox.showerror("错误", f"地址与现有灯具 '{existing['name']}' 冲突")
                    return

            # 创建配接记录
            patch_record = {
                'id': len(self.configured_fixtures) + 1,
                'name': name,
                'fixture_type': fixture_def['id'],
                'manufacturer': manufacturer,
                'fixture_name': fixture_name,
                'start_address': start_address,
                'universe': universe,
                'zone': zone,
                'mode': mode,
                'channel_count': channel_count,
                'channels': fixture_def['channels'],
                'physical': fixture_def.get('physical', {}),
                'color_wheels': fixture_def.get('color_wheels', []),
                'gobo_wheels': fixture_def.get('gobo_wheels', [])
            }

            self.configured_fixtures.append(patch_record)
            self.update_patch_display()

            # 自动递增地址
            self.start_address_var.set(str(start_address + channel_count))

            self.status_label.config(text=f"已添加灯具: {name} (地址 {start_address}-{start_address + channel_count - 1})")

        except ValueError as e:
            messagebox.showerror("错误", f"参数错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"添加灯具失败: {e}")

    def update_patch_display(self):
        """更新配接显示"""
        # 清除现有数据
        for item in self.patch_tree.get_children():
            self.patch_tree.delete(item)

        # 添加配接数据
        for patch in self.configured_fixtures:
            self.patch_tree.insert("", "end", values=(
                patch['id'],
                patch['name'],
                patch['fixture_name'],
                patch['manufacturer'],
                f"{patch['start_address']}-{patch['start_address'] + patch['channel_count'] - 1}",
                patch['universe'],
                patch['zone'],
                patch['mode'],
                patch['channel_count']
            ))

        # 更新统计
        total_fixtures = len(self.configured_fixtures)
        total_channels = sum(f['channel_count'] for f in self.configured_fixtures)
        universes = len(set(f['universe'] for f in self.configured_fixtures))

        self.patch_stats_label.config(text=f"配接统计: {total_fixtures}个灯具, {total_channels}个通道, {universes}个Universe")

    def show_patch_context_menu(self, event):
        """显示配接右键菜单"""
        selection = self.patch_tree.selection()
        if not selection:
            return

        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="删除", command=lambda: self.delete_patch(selection[0]))
        context_menu.add_command(label="编辑", command=lambda: self.edit_patch(selection[0]))
        context_menu.add_command(label="复制", command=lambda: self.copy_patch(selection[0]))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def delete_patch(self, item_id):
        """删除配接"""
        item = self.patch_tree.item(item_id)
        patch_id = item['values'][0]

        # 确认删除
        if messagebox.askyesno("确认", f"确定要删除灯具 '{item['values'][1]}' 吗？"):
            # 从列表中移除
            self.configured_fixtures = [f for f in self.configured_fixtures if f['id'] != patch_id]

            # 重新编号
            for i, fixture in enumerate(self.configured_fixtures):
                fixture['id'] = i + 1

            self.update_patch_display()
            self.status_label.config(text=f"已删除灯具: {item['values'][1]}")

    def edit_patch(self, item_id):
        """编辑配接"""
        # 这里可以实现编辑功能
        messagebox.showinfo("提示", "编辑功能开发中...")

    def copy_patch(self, item_id):
        """复制配接"""
        item = self.patch_tree.item(item_id)
        patch_id = item['values'][0]

        # 查找原配接
        original = None
        for f in self.configured_fixtures:
            if f['id'] == patch_id:
                original = f
                break

        if original:
            # 创建副本
            new_patch = original.copy()
            new_patch['id'] = len(self.configured_fixtures) + 1
            new_patch['name'] = f"{original['name']}_copy"
            new_patch['start_address'] = original['start_address'] + original['channel_count']

            self.configured_fixtures.append(new_patch)
            self.update_patch_display()
            self.status_label.config(text=f"已复制灯具: {new_patch['name']}")

    def clear_all_patches(self):
        """清空所有配接"""
        if messagebox.askyesno("确认", "确定要清空所有配接吗？"):
            self.configured_fixtures.clear()
            self.update_patch_display()
            self.status_label.config(text="已清空所有配接")

    def save_patch_config(self):
        """保存配接配置"""
        if not self.configured_fixtures:
            messagebox.showwarning("警告", "没有可保存的配接")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存配接配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                config_data = {
                    'fixtures': self.configured_fixtures,
                    'save_time': datetime.now().isoformat(),
                    'version': '1.0'
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配接配置已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def load_patch_config(self):
        """加载配接配置"""
        file_path = filedialog.askopenfilename(
            title="加载配接配置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                self.configured_fixtures = config_data.get('fixtures', [])
                self.update_patch_display()

                messagebox.showinfo("成功", f"已加载 {len(self.configured_fixtures)} 个灯具配接")
            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {e}")

    def generate_ai_sequence(self):
        """生成AI灯光序列"""
        if not self.current_music_analysis:
            messagebox.showwarning("警告", "请先完成音乐分析")
            return

        if not self.configured_fixtures:
            messagebox.showwarning("警告", "请先配接灯具")
            return

        # 在新线程中生成序列
        generate_thread = threading.Thread(target=self._generate_ai_sequence_thread, daemon=True)
        generate_thread.start()

    def _generate_ai_sequence_thread(self):
        """AI序列生成线程"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="AI正在生成灯光序列..."))
            self.root.after(0, lambda: self.progress_var.set(0))

            analysis = self.current_music_analysis
            beat_times = analysis['beat_times']
            energy_profile = analysis['energy_profile']
            style = analysis['style']
            mood = analysis['mood']

            # 获取AI参数
            creativity = self.creativity_var.get()
            sync_precision = self.sync_precision_var.get()
            intensity_factor = self.intensity_scale.get()
            color_richness = self.color_richness_scale.get()

            self.current_sequence = []
            total_beats = len(beat_times)

            # 根据风格和情绪确定基础效果
            base_effects = self._get_ai_base_effects(style, mood, creativity)

            # 为每个节拍生成灯光事件
            for i, beat_time in enumerate(beat_times):
                # 更新进度
                progress = (i / total_beats) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

                # 计算当前能量级别
                energy_index = min(int(beat_time / 10), len(energy_profile) - 1)
                current_energy = energy_profile[energy_index] * intensity_factor

                # 为每个配接的灯具生成事件
                for fixture in self.configured_fixtures:
                    events = self._generate_fixture_events(
                        fixture, beat_time, current_energy, base_effects,
                        i, color_richness, sync_precision
                    )
                    self.current_sequence.extend(events)

                # 模拟处理时间
                if i % 10 == 0:
                    time.sleep(0.01)

            # 排序序列
            self.current_sequence.sort(key=lambda x: x['time'])

            # 更新界面显示
            self.root.after(0, self.update_sequence_display)
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_label.config(text=f"AI序列生成完成，共 {len(self.current_sequence)} 个事件"))

            # 延迟重置进度条
            self.root.after(2000, lambda: self.progress_var.set(0))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"AI序列生成失败: {e}"))

    def _get_ai_base_effects(self, style, mood, creativity):
        """获取AI基础效果"""
        effects = {
            "慢板/抒情": {
                "colors": ["暖白", "琥珀", "淡蓝", "淡紫"],
                "intensity_range": [0.3, 0.7],
                "change_frequency": 4,
                "movement_speed": "慢"
            },
            "中板/流行": {
                "colors": ["白", "红", "蓝", "绿", "黄"],
                "intensity_range": [0.5, 0.8],
                "change_frequency": 2,
                "movement_speed": "中"
            },
            "快板/摇滚": {
                "colors": ["红", "白", "蓝", "黄", "紫"],
                "intensity_range": [0.7, 1.0],
                "change_frequency": 1,
                "movement_speed": "快"
            },
            "电子/舞曲": {
                "colors": ["彩色循环", "霓虹色", "频闪"],
                "intensity_range": [0.8, 1.0],
                "change_frequency": 0.5,
                "movement_speed": "极快"
            }
        }

        base = effects.get(style, effects["中板/流行"])

        # 根据创意级别调整
        if creativity == "创新":
            base["colors"].extend(["青", "橙", "粉"])
            base["change_frequency"] *= 0.8
        elif creativity == "极致":
            base["colors"].extend(["青", "橙", "粉", "lime", "深蓝"])
            base["change_frequency"] *= 0.6

        return base

    def _generate_fixture_events(self, fixture, beat_time, energy, base_effects, beat_index, color_richness, sync_precision):
        """为单个灯具生成事件"""
        events = []

        # 根据同步精度决定是否在此节拍生成事件
        sync_factor = {"低": 4, "中": 2, "高": 1, "极高": 0.5}[sync_precision]
        if beat_index % max(1, int(sync_factor)) != 0:
            return events

        # 基础强度
        min_intensity, max_intensity = base_effects["intensity_range"]
        intensity = min_intensity + (max_intensity - min_intensity) * energy

        # 生成DMX事件
        dmx_values = {}

        # 调光通道
        if 'dimmer' in fixture['channels']:
            dmx_values['dimmer'] = int(intensity * 255)

        # 颜色通道
        if any(color in fixture['channels'] for color in ['red', 'green', 'blue']):
            color_values = self._generate_color_values(base_effects["colors"], beat_index, color_richness, intensity)
            dmx_values.update(color_values)

        # 移动通道（摇头灯）
        if 'pan' in fixture['channels'] and 'tilt' in fixture['channels']:
            movement_values = self._generate_movement_values(fixture, beat_time, energy, base_effects["movement_speed"])
            dmx_values.update(movement_values)

        # 其他效果通道
        if 'gobo' in fixture['channels']:
            dmx_values['gobo'] = self._generate_gobo_value(fixture, beat_index, energy)

        if 'strobe' in fixture['channels']:
            dmx_values['strobe'] = self._generate_strobe_value(energy, base_effects["change_frequency"])

        # 创建事件
        if dmx_values:
            event = {
                'time': beat_time,
                'fixture_id': fixture['id'],
                'fixture_name': fixture['name'],
                'universe': fixture['universe'],
                'start_address': fixture['start_address'],
                'dmx_values': dmx_values,
                'energy_level': energy,
                'zone': fixture['zone']
            }
            events.append(event)

        return events

    def _generate_color_values(self, color_palette, beat_index, color_richness, intensity):
        """生成颜色值"""
        color_map = {
            "红": (255, 0, 0), "绿": (0, 255, 0), "蓝": (0, 0, 255),
            "白": (255, 255, 255), "黄": (255, 255, 0), "紫": (255, 0, 255),
            "青": (0, 255, 255), "橙": (255, 165, 0), "粉": (255, 192, 203),
            "暖白": (255, 200, 150), "琥珀": (255, 191, 0), "淡蓝": (173, 216, 230),
            "淡紫": (221, 160, 221), "lime": (50, 205, 50), "深蓝": (0, 0, 139)
        }

        # 选择颜色
        if "彩色循环" in color_palette:
            # 彩虹循环
            hue = (beat_index * 30) % 360
            rgb = self._hsv_to_rgb(hue, color_richness, intensity)
        else:
            # 从调色板选择
            color_name = color_palette[beat_index % len(color_palette)]
            if color_name in color_map:
                base_rgb = color_map[color_name]
                rgb = tuple(int(c * intensity * color_richness) for c in base_rgb)
            else:
                rgb = (int(255 * intensity), int(255 * intensity), int(255 * intensity))

        return {
            'red': min(255, rgb[0]),
            'green': min(255, rgb[1]),
            'blue': min(255, rgb[2])
        }

    def _hsv_to_rgb(self, h, s, v):
        """HSV转RGB"""
        import colorsys
        r, g, b = colorsys.hsv_to_rgb(h/360, s, v)
        return (int(r * 255), int(g * 255), int(b * 255))

    def _generate_movement_values(self, fixture, beat_time, energy, speed):
        """生成移动值"""
        speed_factor = {"慢": 0.3, "中": 0.6, "快": 0.8, "极快": 1.0}[speed]

        # 基于时间和能量的摆动
        pan_base = 127 + int(100 * energy * speed_factor * (1 if int(beat_time) % 2 == 0 else -1))
        tilt_base = 127 + int(50 * energy * speed_factor * (1 if int(beat_time / 2) % 2 == 0 else -1))

        return {
            'pan': max(0, min(255, pan_base)),
            'tilt': max(0, min(255, tilt_base))
        }

    def _generate_gobo_value(self, fixture, beat_index, energy):
        """生成图案轮值"""
        gobo_wheels = fixture.get('gobo_wheels', [])
        if gobo_wheels and gobo_wheels[0].get('gobos'):
            gobos = gobo_wheels[0]['gobos']
            # 根据能量和节拍选择图案
            if energy > 0.7:
                gobo_index = beat_index % len(gobos)
            else:
                gobo_index = 0  # 开放
            return gobos[gobo_index]['value']
        return 0

    def _generate_strobe_value(self, energy, change_frequency):
        """生成频闪值"""
        if energy > 0.8 and change_frequency < 1:
            return int(200 + 55 * energy)  # 高速频闪
        elif energy > 0.6:
            return int(150 + 50 * energy)  # 中速频闪
        else:
            return 0  # 无频闪

    def update_sequence_display(self):
        """更新序列显示"""
        # 清除现有数据
        for item in self.sequence_tree.get_children():
            self.sequence_tree.delete(item)

        # 显示前100个事件
        display_count = min(100, len(self.current_sequence))

        for i, event in enumerate(self.current_sequence[:display_count]):
            time_str = f"{event['time']:.2f}s"
            fixture_name = event['fixture_name']
            zone = event['zone']

            # 格式化通道信息
            channels_str = ", ".join(event['dmx_values'].keys())

            # 计算平均值
            avg_value = sum(event['dmx_values'].values()) / len(event['dmx_values'])
            value_str = f"{avg_value:.0f}"

            # 效果描述
            effect_str = "移动" if any(ch in event['dmx_values'] for ch in ['pan', 'tilt']) else "静态"
            if 'strobe' in event['dmx_values'] and event['dmx_values']['strobe'] > 0:
                effect_str += "+频闪"

            # 颜色描述
            if all(ch in event['dmx_values'] for ch in ['red', 'green', 'blue']):
                r, g, b = event['dmx_values']['red'], event['dmx_values']['green'], event['dmx_values']['blue']
                color_str = f"RGB({r},{g},{b})"
            else:
                color_str = "单色"

            self.sequence_tree.insert("", "end", values=(
                time_str, fixture_name, zone, channels_str, value_str, effect_str, color_str
            ))

        # 更新统计
        total_events = len(self.current_sequence)
        duration = self.current_music_analysis['duration'] if self.current_music_analysis else 0

        self.sequence_stats_label.config(text=f"序列统计: {total_events}个事件, 时长{duration:.1f}秒, 显示前{display_count}个")

    def preview_sequence(self):
        """预览序列"""
        if not self.current_sequence:
            messagebox.showwarning("警告", "没有可预览的序列")
            return

        messagebox.showinfo("预览", f"序列预览功能\n\n总事件数: {len(self.current_sequence)}\n建议连接Art-Net进行实时预览")

    def export_sequence(self):
        """导出序列"""
        if not self.current_sequence:
            messagebox.showwarning("警告", "没有可导出的序列")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出AI灯光序列",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("MA2 Show文件", "*.xml"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                export_data = {
                    'music_analysis': self.current_music_analysis,
                    'fixtures': self.configured_fixtures,
                    'sequence': self.current_sequence,
                    'ai_parameters': {
                        'creativity': self.creativity_var.get(),
                        'sync_precision': self.sync_precision_var.get(),
                        'intensity_factor': self.intensity_scale.get(),
                        'color_richness': self.color_richness_scale.get()
                    },
                    'export_time': datetime.now().isoformat(),
                    'version': '1.0'
                }

                if file_path.endswith('.xml'):
                    # 导出为MA2格式（简化）
                    self._export_ma2_format(file_path, export_data)
                else:
                    # 导出为JSON格式
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"序列已导出到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def _export_ma2_format(self, file_path, data):
        """导出为MA2格式"""
        # 这里可以实现MA2 XML格式的导出
        # 简化实现：转换为MA2可识别的格式
        ma2_data = {
            'show_info': {
                'name': 'AI Generated Show',
                'created': data['export_time']
            },
            'fixtures': data['fixtures'],
            'sequence': data['sequence']
        }

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(ma2_data, f, indent=2, ensure_ascii=False)

    def regenerate_sequence(self):
        """重新生成序列"""
        if messagebox.askyesno("确认", "确定要重新生成序列吗？当前序列将被覆盖。"):
            self.generate_ai_sequence()

    # ==================== Art-Net和实时控制 ====================

    def connect_artnet(self):
        """连接Art-Net"""
        try:
            target_ip = self.artnet_ip_var.get()
            target_port = int(self.artnet_port_var.get())

            # 创建UDP socket
            self.artnet_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.artnet_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)

            # 测试连接
            test_packet = self._create_artnet_packet(0, [0] * 512)
            self.artnet_socket.sendto(test_packet, (target_ip, target_port))

            self.connection_status_label.config(text="🟢 已连接", fg="#10b981")
            self.status_label.config(text=f"Art-Net已连接: {target_ip}:{target_port}")

            self.connect_btn.config(state="disabled")
            self.disconnect_btn.config(state="normal")

            messagebox.showinfo("成功", f"Art-Net连接成功！\n目标: {target_ip}:{target_port}")

        except Exception as e:
            messagebox.showerror("错误", f"Art-Net连接失败: {e}")
            self.connection_status_label.config(text="🔴 连接失败", fg="#ef4444")

    def disconnect_artnet(self):
        """断开Art-Net连接"""
        if self.artnet_socket:
            self.artnet_socket.close()
            self.artnet_socket = None

        self.connection_status_label.config(text="🔴 未连接", fg="#ef4444")
        self.status_label.config(text="Art-Net连接已断开")

        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")

        # 停止播放
        if self.is_playing:
            self.stop_playback()

    def _create_artnet_packet(self, universe, dmx_data):
        """创建Art-Net数据包"""
        packet = bytearray()
        packet.extend(b"Art-Net\x00")  # Art-Net ID
        packet.extend(struct.pack("<H", 0x5000))  # OpDmx
        packet.extend(struct.pack(">H", 14))  # Protocol version
        packet.append(0)  # Sequence
        packet.append(0)  # Physical
        packet.append(universe & 0xFF)  # Universe low
        packet.append((universe >> 8) & 0xFF)  # Universe high
        packet.extend(struct.pack(">H", 512))  # Data length

        # 确保DMX数据长度为512
        if len(dmx_data) < 512:
            dmx_data.extend([0] * (512 - len(dmx_data)))
        elif len(dmx_data) > 512:
            dmx_data = dmx_data[:512]

        packet.extend(dmx_data)
        return packet

    def start_playback(self):
        """开始播放"""
        if not self.current_sequence:
            messagebox.showwarning("警告", "请先生成AI灯光序列")
            return

        if not self.artnet_socket:
            messagebox.showwarning("警告", "请先连接Art-Net")
            return

        if not self.is_playing:
            self.is_playing = True
            self.sequence_index = 0
            self.start_time = time.time()

            self.play_btn.config(text="⏸️ 暂停")
            self.playback_thread = threading.Thread(target=self._playback_thread, daemon=True)
            self.playback_thread.start()

            self.status_label.config(text="开始播放AI灯光序列...")

    def pause_playback(self):
        """暂停播放"""
        if self.is_playing:
            self.is_playing = False
            self.play_btn.config(text="▶️ 播放")
            self.status_label.config(text="播放已暂停")
        else:
            # 恢复播放
            self.is_playing = True
            self.start_time = time.time() - (self.sequence_index * 0.1)  # 调整开始时间
            self.play_btn.config(text="⏸️ 暂停")
            self.playback_thread = threading.Thread(target=self._playback_thread, daemon=True)
            self.playback_thread.start()
            self.status_label.config(text="恢复播放...")

    def stop_playback(self):
        """停止播放"""
        self.is_playing = False
        self.sequence_index = 0

        self.play_btn.config(text="▶️ 播放")
        self.playback_progress_var.set(0)
        self.time_label.config(text="00:00 / 00:00")
        self.status_label.config(text="播放已停止")

        # 清除所有灯具输出
        self._clear_all_outputs()

        # 清除活跃灯具显示
        for item in self.active_tree.get_children():
            self.active_tree.delete(item)

    def _playback_thread(self):
        """播放线程"""
        if not self.current_sequence or not self.current_music_analysis:
            return

        total_duration = self.current_music_analysis['duration']
        sorted_sequence = sorted(self.current_sequence, key=lambda x: x['time'])

        sent_packets = 0

        while self.is_playing and self.sequence_index < len(sorted_sequence):
            current_time = time.time() - self.start_time

            # 更新进度显示
            progress = (current_time / total_duration) * 100 if total_duration > 0 else 0
            self.root.after(0, lambda: self.playback_progress_var.set(min(progress, 100)))

            # 更新时间显示
            time_str = f"{int(current_time//60):02d}:{int(current_time%60):02d} / {int(total_duration//60):02d}:{int(total_duration%60):02d}"
            self.root.after(0, lambda: self.time_label.config(text=time_str))

            # 更新系统状态
            bpm = self.current_music_analysis.get('bpm', 0)
            self.root.after(0, lambda: self.current_time_label.config(text=f"播放时间: {current_time:.1f}s"))
            self.root.after(0, lambda: self.current_bpm_label.config(text=f"当前BPM: {bpm:.1f}"))
            self.root.after(0, lambda: self.sent_packets_label.config(text=f"发送包数: {sent_packets}"))

            # 处理当前时间的事件
            events_to_send = []
            while (self.sequence_index < len(sorted_sequence) and
                   sorted_sequence[self.sequence_index]['time'] <= current_time):

                events_to_send.append(sorted_sequence[self.sequence_index])
                self.sequence_index += 1

            # 发送Art-Net数据
            if events_to_send:
                universes_data = self._prepare_universe_data(events_to_send)

                for universe, dmx_data in universes_data.items():
                    try:
                        packet = self._create_artnet_packet(universe, dmx_data)
                        target_ip = self.artnet_ip_var.get()
                        target_port = int(self.artnet_port_var.get())
                        self.artnet_socket.sendto(packet, (target_ip, target_port))
                        sent_packets += 1
                    except Exception as e:
                        print(f"Art-Net发送错误: {e}")

                # 更新活跃灯具显示
                self.root.after(0, lambda: self._update_active_fixtures_display(events_to_send))

            # 检查是否播放完成
            if current_time >= total_duration:
                break

            time.sleep(0.02)  # 50Hz更新频率

        # 播放完成
        self.root.after(0, self.stop_playback)

    def _prepare_universe_data(self, events):
        """准备Universe数据"""
        universes_data = {}

        for event in events:
            universe = event['universe']
            start_address = event['start_address']
            dmx_values = event['dmx_values']

            # 初始化Universe数据
            if universe not in universes_data:
                universes_data[universe] = [0] * 512

            # 查找灯具定义以获取通道映射
            fixture = None
            for f in self.configured_fixtures:
                if f['id'] == event['fixture_id']:
                    fixture = f
                    break

            if fixture:
                # 根据灯具定义映射通道
                channel_mapping = fixture['channels']

                for channel_name, value in dmx_values.items():
                    if channel_name in channel_mapping:
                        # 计算实际DMX地址
                        channel_info = channel_mapping[channel_name]
                        if isinstance(channel_info, dict) and 'channel' in channel_info:
                            channel_offset = channel_info['channel'] - 1  # 转换为0基址
                        else:
                            # 简化处理：按顺序分配
                            channel_names = list(channel_mapping.keys())
                            channel_offset = channel_names.index(channel_name)

                        dmx_address = start_address + channel_offset - 1  # DMX地址从1开始

                        if 0 <= dmx_address < 512:
                            universes_data[universe][dmx_address] = max(0, min(255, int(value)))

        return universes_data

    def _update_active_fixtures_display(self, events):
        """更新活跃灯具显示"""
        # 清除现有显示
        for item in self.active_tree.get_children():
            self.active_tree.delete(item)

        # 按灯具分组事件
        fixture_events = {}
        for event in events:
            fixture_id = event['fixture_id']
            if fixture_id not in fixture_events:
                fixture_events[fixture_id] = []
            fixture_events[fixture_id].append(event)

        # 显示每个活跃灯具
        active_count = 0
        for fixture_id, events_list in fixture_events.items():
            # 合并同一灯具的所有事件
            combined_event = events_list[0]  # 使用第一个事件作为基础

            # 计算平均强度
            all_values = []
            for event in events_list:
                all_values.extend(event['dmx_values'].values())

            avg_intensity = sum(all_values) / len(all_values) if all_values else 0

            # 颜色信息
            dmx_values = combined_event['dmx_values']
            if all(ch in dmx_values for ch in ['red', 'green', 'blue']):
                color_str = f"RGB({dmx_values['red']},{dmx_values['green']},{dmx_values['blue']})"
            elif 'dimmer' in dmx_values:
                color_str = f"调光({dmx_values['dimmer']})"
            else:
                color_str = "混合"

            # 状态
            status = "活跃" if avg_intensity > 10 else "待机"
            if status == "活跃":
                active_count += 1

            self.active_tree.insert("", "end", values=(
                combined_event['fixture_name'],
                combined_event['universe'],
                f"{combined_event['start_address']}-{combined_event['start_address'] + len(dmx_values) - 1}",
                f"{avg_intensity:.0f}",
                color_str,
                status
            ))

        # 更新活跃灯具计数
        self.active_fixtures_label.config(text=f"活跃灯具: {active_count}")

    def _clear_all_outputs(self):
        """清除所有输出"""
        if not self.artnet_socket:
            return

        try:
            # 获取所有使用的Universe
            universes = set(f['universe'] for f in self.configured_fixtures)

            # 发送全零数据包
            for universe in universes:
                packet = self._create_artnet_packet(universe, [0] * 512)
                target_ip = self.artnet_ip_var.get()
                target_port = int(self.artnet_port_var.get())
                self.artnet_socket.sendto(packet, (target_ip, target_port))

        except Exception as e:
            print(f"清除输出错误: {e}")

    def run(self):
        """运行应用"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

    def on_closing(self):
        """关闭应用"""
        # 停止播放
        if self.is_playing:
            self.stop_playback()

        # 断开Art-Net连接
        if self.artnet_socket:
            self.disconnect_artnet()

        self.root.destroy()

if __name__ == "__main__":
    print("🎭 MA控台专业AI灯光系统启动中...")

    try:
        app = MALightingSystem()
        print("✅ 系统初始化完成")
        print("\n💡 使用步骤:")
        print("   1. 📚 导入MA XML灯具库")
        print("   2. 🎵 导入音乐文件并AI分析")
        print("   3. 🔌 配接灯具到相应区域")
        print("   4. 🤖 生成AI灯光序列")
        print("   5. 🔗 连接Art-Net到MA控台")
        print("   6. ▶️ 播放并在MA控台查看效果")
        print("\n🎯 支持GrandMA2/MA3控台XML灯具库格式")
        print("🌐 Art-Net输出直接到MA控台 (默认IP: *********)")

        app.run()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()