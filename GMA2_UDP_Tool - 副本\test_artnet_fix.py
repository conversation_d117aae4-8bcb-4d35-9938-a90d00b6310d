#!/usr/bin/env python3
"""
Art-Net功能修复测试脚本
用于验证Art-Net功能是否正常工作
"""

import socket
import struct
import time
import sys

def create_artnet_packet(universe, subnet, net, dmx_data):
    """创建标准Art-Net数据包"""
    try:
        # 确保DMX数据长度为512
        if len(dmx_data) < 512:
            dmx_data = list(dmx_data) + [0] * (512 - len(dmx_data))
        elif len(dmx_data) > 512:
            dmx_data = dmx_data[:512]

        # Art-Net头部 - 严格按照Art-Net 4标准
        header = b"Art-Net\x00"  # 8字节标识符
        opcode = struct.pack("<H", 0x5000)  # OpDmx (小端序)
        protocol_version = struct.pack(">H", 14)  # 协议版本14 (大端序)
        sequence = struct.pack("B", 0)  # 序列号 (0表示不使用序列)
        physical = struct.pack("B", 0)  # 物理输入端口

        # Universe地址计算 - 标准Art-Net格式
        universe_low = universe & 0xFF
        universe_high = ((net & 0x7F) << 1) | ((subnet & 0x0F) << 4) | ((universe >> 8) & 0x0F)
        universe_bytes = struct.pack("BB", universe_low, universe_high)

        # 数据长度 - 必须是偶数，最小2，最大512
        data_length = len(dmx_data)
        if data_length % 2 != 0:
            data_length += 1
            dmx_data.append(0)
        
        length = struct.pack(">H", data_length)  # 大端序

        # 组装完整的Art-Net数据包
        packet = (header + opcode + protocol_version + sequence + 
                 physical + universe_bytes + length + bytes(dmx_data))

        return packet

    except Exception as e:
        print(f"❌ 创建Art-Net数据包失败: {e}")
        return None

def send_artnet_packet(packet_data, target_ip, port=6454):
    """发送Art-Net数据包"""
    try:
        # 创建UDP套接字
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        
        # 发送数据包
        sock.sendto(packet_data, (target_ip, port))
        sock.close()
        
        print(f"✅ Art-Net数据包已发送到 {target_ip}:{port}")
        print(f"   数据包大小: {len(packet_data)} 字节")
        return True
        
    except Exception as e:
        print(f"❌ Art-Net数据包发送失败: {e}")
        return False

def test_artnet_basic():
    """基本Art-Net测试"""
    print("🎭 Art-Net基本功能测试")
    print("=" * 50)
    
    universe = 1
    subnet = 0
    net = 0
    target_ip = "***************"
    
    print(f"配置: Universe={universe}, Subnet={subnet}, Net={net}")
    print(f"目标: {target_ip}:6454")
    print()
    
    # 测试1: 简单测试数据包
    print("🧪 测试1: 简单测试数据包")
    dmx_data = [255] * 10 + [0] * 502  # 前10个通道全亮
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        success = send_artnet_packet(packet, target_ip)
        if success:
            print("   ✅ 基本测试通过")
        else:
            print("   ❌ 基本测试失败")
    else:
        print("   ❌ 数据包创建失败")
    
    time.sleep(1)
    
    # 测试2: 全亮测试
    print("💡 测试2: 全亮测试")
    dmx_data = [255] * 512
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        success = send_artnet_packet(packet, target_ip)
        if success:
            print("   ✅ 全亮测试通过")
        else:
            print("   ❌ 全亮测试失败")
    else:
        print("   ❌ 数据包创建失败")
    
    time.sleep(1)
    
    # 测试3: 全暗测试
    print("🌑 测试3: 全暗测试")
    dmx_data = [0] * 512
    
    packet = create_artnet_packet(universe, subnet, net, dmx_data)
    if packet:
        success = send_artnet_packet(packet, target_ip)
        if success:
            print("   ✅ 全暗测试通过")
        else:
            print("   ❌ 全暗测试失败")
    else:
        print("   ❌ 数据包创建失败")
    
    print()
    print("✅ Art-Net基本功能测试完成")

def test_network_connectivity():
    """测试网络连通性"""
    print("🌐 网络连通性测试")
    print("=" * 50)
    
    try:
        # 测试UDP端口6454
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.bind(('', 6454))
        sock.close()
        print("✅ Art-Net端口 6454 可用")
    except Exception as e:
        print(f"❌ Art-Net端口 6454 不可用: {e}")
    
    try:
        # 测试广播功能
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        test_data = b"TEST_BROADCAST"
        sock.sendto(test_data, ('***************', 6454))
        sock.close()
        print("✅ 广播功能正常")
    except Exception as e:
        print(f"❌ 广播功能异常: {e}")
    
    print()

if __name__ == "__main__":
    print("🎭 Art-Net功能修复测试工具")
    print("用于验证Art-Net功能是否正常工作")
    print()
    
    # 测试网络连通性
    test_network_connectivity()
    
    # 测试Art-Net基本功能
    test_artnet_basic()
    
    print()
    print("🎯 测试完成！")
    print()
    print("💡 使用说明:")
    print("1. 如果所有测试都通过，说明Art-Net功能正常")
    print("2. 如果有测试失败，请检查网络配置和防火墙设置")
    print("3. 确保目标设备支持Art-Net协议并在同一网络")
    print("4. 可以使用专业Art-Net软件验证数据包接收")
