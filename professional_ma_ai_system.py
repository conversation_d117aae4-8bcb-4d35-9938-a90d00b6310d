#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业MA AI灯光系统 - 完整版
集成真实MA XML解析器 + AI音乐分析 + Art-Net输出
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import socket
import struct
import threading
import time
import random
from datetime import datetime
from real_ma_parser import RealMAParser
from simple_music_analyzer import SimpleMusicAnalyzer
import pygame  # 用于音乐播放
import numpy as np

class ProfessionalMASystem:
    """专业MA AI灯光系统"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 专业MA AI灯光系统")
        self.root.geometry("1600x1000")
        self.root.configure(bg="#0f172a")
        
        # 初始化组件
        self.ma_parser = RealMAParser()
        self.music_analyzer = SimpleMusicAnalyzer()
        self.configured_fixtures = []
        self.music_analysis = None
        self.lighting_sequence = []
        self.artnet_socket = None
        self.is_playing = False
        self.playback_thread = None

        # 音乐播放相关
        pygame.mixer.init()
        self.current_music_file = None
        self.music_loaded = False

        # 数据存储
        self.universe_data = {}
        self.sequence_index = 0
        self.start_time = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题栏
        title_frame = tk.Frame(self.root, bg="#0f172a", height=80)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)
        
        # 标题
        title_label = tk.Label(title_frame, text="🎭 专业MA AI灯光系统", 
                              font=("Arial", 22, "bold"), fg="#f8fafc", bg="#0f172a")
        title_label.pack(pady=15)
        
        subtitle_label = tk.Label(title_frame, text="真实MA XML灯具库 → AI音乐分析 → 智能序列生成 → Art-Net输出 → MA控台", 
                                 font=("Arial", 12), fg="#94a3b8", bg="#0f172a")
        subtitle_label.pack()
        
        # 主要内容区域
        main_frame = tk.Frame(self.root, bg="#1e293b")
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建选项卡
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('TNotebook', background='#1e293b')
        style.configure('TNotebook.Tab', background='#334155', foreground='white', padding=[20, 10])
        style.map('TNotebook.Tab', background=[('selected', '#0ea5e9')])
        
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill="both", expand=True)
        
        # 1. MA灯具库管理
        self.create_fixture_library_tab()
        
        # 2. 音乐AI分析
        self.create_music_analysis_tab()
        
        # 3. 灯具配接
        self.create_fixture_patching_tab()
        
        # 4. AI序列生成
        self.create_ai_sequence_tab()
        
        # 5. 实时控制
        self.create_realtime_control_tab()
        
        # 状态栏
        self.create_status_bar()
        
    def create_fixture_library_tab(self):
        """创建MA灯具库管理选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="📚 MA灯具库")
        
        # 灯具库导入区域
        import_frame = tk.LabelFrame(frame, text="MA XML灯具库导入", 
                                    font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        import_frame.pack(fill="x", padx=10, pady=5)
        
        # 文件选择
        file_frame = tk.Frame(import_frame, bg="#1e293b")
        file_frame.pack(fill="x", padx=10, pady=5)
        
        self.xml_file_var = tk.StringVar()
        xml_entry = tk.Entry(file_frame, textvariable=self.xml_file_var, width=80, 
                            font=("Arial", 10), bg="#334155", fg="white")
        xml_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        browse_xml_btn = tk.Button(file_frame, text="📁 选择MA XML", 
                                  command=self.browse_ma_xml_file,
                                  bg="#0ea5e9", fg="white", font=("Arial", 10, "bold"))
        browse_xml_btn.pack(side="right", padx=5)
        
        parse_btn = tk.Button(file_frame, text="🔍 解析灯具库", 
                             command=self.parse_ma_xml,
                             bg="#059669", fg="white", font=("Arial", 10, "bold"))
        parse_btn.pack(side="right", padx=5)
        
        # 快速加载按钮
        quick_frame = tk.Frame(import_frame, bg="#1e293b")
        quick_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(quick_frame, text="快速加载:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        
        tk.Button(quick_frame, text="ACME AECO 20", 
                 command=lambda: self.load_sample_fixture("acme@<EMAIL>"),
                 bg="#7c3aed", fg="white", font=("Arial", 9)).pack(side="left", padx=2)
        
        tk.Button(quick_frame, text="示例灯具库", 
                 command=lambda: self.load_sample_fixture("sample_ma_fixtures.xml"),
                 bg="#7c3aed", fg="white", font=("Arial", 9)).pack(side="left", padx=2)
        
        # 灯具库显示区域
        library_frame = tk.LabelFrame(frame, text="已加载的MA灯具库", 
                                     font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        library_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 搜索和筛选
        search_frame = tk.Frame(library_frame, bg="#1e293b")
        search_frame.pack(fill="x", padx=5, pady=5)
        
        tk.Label(search_frame, text="搜索:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=30,
                               font=("Arial", 10), bg="#334155", fg="white")
        search_entry.pack(side="left", padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        # 制造商筛选
        tk.Label(search_frame, text="制造商:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.manufacturer_var = tk.StringVar(value="全部")
        self.manufacturer_combo = ttk.Combobox(search_frame, textvariable=self.manufacturer_var, 
                                              width=20, state="readonly")
        self.manufacturer_combo.pack(side="left", padx=5)
        self.manufacturer_combo.bind('<<ComboboxSelected>>', self.on_manufacturer_change)
        
        # 灯具列表
        list_frame = tk.Frame(library_frame, bg="#1e293b")
        list_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # 创建表格
        columns = ("制造商", "灯具名称", "短名称", "最大通道", "模式数", "功率")
        self.fixture_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=20)
        
        # 设置列标题和宽度
        column_widths = {"制造商": 100, "灯具名称": 250, "短名称": 200, "最大通道": 80, "模式数": 80, "功率": 80}
        for col in columns:
            self.fixture_tree.heading(col, text=col)
            self.fixture_tree.column(col, width=column_widths.get(col, 100))
        
        # 滚动条
        fixture_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=self.fixture_tree.yview)
        self.fixture_tree.configure(yscrollcommand=fixture_scroll.set)
        
        self.fixture_tree.pack(side="left", fill="both", expand=True)
        fixture_scroll.pack(side="right", fill="y")
        
        # 双击查看详情
        self.fixture_tree.bind('<Double-1>', self.show_fixture_details)
        
        # 统计信息
        stats_frame = tk.Frame(library_frame, bg="#1e293b")
        stats_frame.pack(fill="x", padx=5, pady=5)
        
        self.stats_label = tk.Label(stats_frame, text="MA灯具库统计: 未加载", 
                                   font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.stats_label.pack(side="left")
        
        # 导出按钮
        export_btn = tk.Button(stats_frame, text="💾 导出JSON", 
                              command=self.export_fixtures_json,
                              bg="#7c3aed", fg="white", font=("Arial", 10))
        export_btn.pack(side="right", padx=5)
        
    def create_music_analysis_tab(self):
        """创建音乐分析选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🎵 音乐AI分析")
        
        # 音乐文件选择
        music_frame = tk.LabelFrame(frame, text="AI音乐分析", 
                                   font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        music_frame.pack(fill="x", padx=10, pady=5)
        
        file_frame = tk.Frame(music_frame, bg="#1e293b")
        file_frame.pack(fill="x", padx=10, pady=5)
        
        self.music_file_var = tk.StringVar()
        music_entry = tk.Entry(file_frame, textvariable=self.music_file_var, width=80,
                              font=("Arial", 10), bg="#334155", fg="white")
        music_entry.pack(side="left", fill="x", expand=True, padx=(0, 5))
        
        browse_music_btn = tk.Button(file_frame, text="📁 选择音乐", 
                                    command=self.browse_music_file,
                                    bg="#0ea5e9", fg="white", font=("Arial", 10, "bold"))
        browse_music_btn.pack(side="right", padx=5)
        
        analyze_btn = tk.Button(file_frame, text="🤖 AI分析", 
                               command=self.analyze_music,
                               bg="#dc2626", fg="white", font=("Arial", 10, "bold"))
        analyze_btn.pack(side="right", padx=5)
        
        # 分析结果显示
        results_frame = tk.LabelFrame(frame, text="AI分析结果", 
                                     font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 左侧：基本信息
        info_frame = tk.Frame(results_frame, bg="#1e293b")
        info_frame.pack(side="left", fill="y", padx=10, pady=10)
        
        tk.Label(info_frame, text="音乐特征", font=("Arial", 14, "bold"), 
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))
        
        self.bpm_label = tk.Label(info_frame, text="BPM: --", font=("Arial", 12), 
                                 fg="#94a3b8", bg="#1e293b")
        self.bpm_label.pack(anchor="w", pady=2)
        
        self.duration_label = tk.Label(info_frame, text="时长: --", font=("Arial", 12), 
                                      fg="#94a3b8", bg="#1e293b")
        self.duration_label.pack(anchor="w", pady=2)
        
        self.style_label = tk.Label(info_frame, text="风格: --", font=("Arial", 12), 
                                   fg="#94a3b8", bg="#1e293b")
        self.style_label.pack(anchor="w", pady=2)
        
        self.mood_label = tk.Label(info_frame, text="情绪: --", font=("Arial", 12), 
                                  fg="#94a3b8", bg="#1e293b")
        self.mood_label.pack(anchor="w", pady=2)
        
        self.beats_label = tk.Label(info_frame, text="节拍数: --", font=("Arial", 12), 
                                   fg="#94a3b8", bg="#1e293b")
        self.beats_label.pack(anchor="w", pady=2)
        
        # 右侧：详细分析
        detail_frame = tk.Frame(results_frame, bg="#1e293b")
        detail_frame.pack(side="right", fill="both", expand=True, padx=10, pady=10)
        
        tk.Label(detail_frame, text="详细分析数据", font=("Arial", 14, "bold"), 
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))
        
        self.analysis_text = tk.Text(detail_frame, height=20, font=("Consolas", 9),
                                    bg="#334155", fg="white", insertbackground="white")
        analysis_scroll = ttk.Scrollbar(detail_frame, orient="vertical", command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scroll.set)
        
        self.analysis_text.pack(side="left", fill="both", expand=True)
        analysis_scroll.pack(side="right", fill="y")
        
    def create_fixture_patching_tab(self):
        """创建灯具配接选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🔌 灯具配接")
        
        # 配接控制区域
        patch_frame = tk.LabelFrame(frame, text="专业灯具配接", 
                                   font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        patch_frame.pack(fill="x", padx=10, pady=5)
        
        # 配接参数
        param_frame = tk.Frame(patch_frame, bg="#1e293b")
        param_frame.pack(fill="x", padx=10, pady=10)
        
        # 第一行参数
        row1 = tk.Frame(param_frame, bg="#1e293b")
        row1.pack(fill="x", pady=5)
        
        tk.Label(row1, text="MA灯具:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.selected_fixture_var = tk.StringVar()
        self.fixture_combo = ttk.Combobox(row1, textvariable=self.selected_fixture_var, 
                                         width=40, state="readonly")
        self.fixture_combo.pack(side="left", padx=5)
        
        tk.Label(row1, text="模式:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.mode_var = tk.StringVar()
        self.mode_combo = ttk.Combobox(row1, textvariable=self.mode_var, 
                                      width=15, state="readonly")
        self.mode_combo.pack(side="left", padx=5)
        
        # 第二行参数
        row2 = tk.Frame(param_frame, bg="#1e293b")
        row2.pack(fill="x", pady=5)
        
        tk.Label(row2, text="起始地址:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.start_address_var = tk.StringVar(value="1")
        tk.Entry(row2, textvariable=self.start_address_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)
        
        tk.Label(row2, text="Universe:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.universe_var = tk.StringVar(value="1")
        tk.Entry(row2, textvariable=self.universe_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)
        
        tk.Label(row2, text="数量:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.count_var = tk.StringVar(value="1")
        tk.Entry(row2, textvariable=self.count_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)
        
        tk.Label(row2, text="区域:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.zone_var = tk.StringVar(value="舞台前区")
        zone_combo = ttk.Combobox(row2, textvariable=self.zone_var, 
                                 values=["舞台前区", "舞台后区", "观众席", "背景区", "特效区", "侧光区"], 
                                 width=15, state="readonly")
        zone_combo.pack(side="left", padx=5)
        
        # 配接按钮
        add_fixture_btn = tk.Button(param_frame, text="➕ 添加灯具配接", 
                                   command=self.add_fixture_patch,
                                   bg="#059669", fg="white", font=("Arial", 12, "bold"))
        add_fixture_btn.pack(pady=10)
        
        # 已配接灯具列表
        patched_frame = tk.LabelFrame(frame, text="已配接灯具列表", 
                                     font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        patched_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建配接表格
        patch_columns = ("ID", "名称", "MA类型", "模式", "地址范围", "Universe", "区域", "通道数")
        self.patch_tree = ttk.Treeview(patched_frame, columns=patch_columns, show="headings", height=15)
        
        # 设置列
        patch_widths = {"ID": 50, "名称": 150, "MA类型": 200, "模式": 80, "地址范围": 100, 
                       "Universe": 70, "区域": 100, "通道数": 70}
        for col in patch_columns:
            self.patch_tree.heading(col, text=col)
            self.patch_tree.column(col, width=patch_widths.get(col, 80))
        
        patch_scroll = ttk.Scrollbar(patched_frame, orient="vertical", command=self.patch_tree.yview)
        self.patch_tree.configure(yscrollcommand=patch_scroll.set)
        
        self.patch_tree.pack(side="left", fill="both", expand=True)
        patch_scroll.pack(side="right", fill="y")
        
        # 配接统计和操作
        patch_ops_frame = tk.Frame(patched_frame, bg="#1e293b")
        patch_ops_frame.pack(fill="x", padx=5, pady=5)
        
        self.patch_stats_label = tk.Label(patch_ops_frame, text="配接统计: 0个灯具", 
                                         font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.patch_stats_label.pack(side="left")
        
        # 操作按钮
        ops_frame = tk.Frame(patch_ops_frame, bg="#1e293b")
        ops_frame.pack(side="right")
        
        tk.Button(ops_frame, text="🗑️ 清空", command=self.clear_all_patches,
                 bg="#dc2626", fg="white", font=("Arial", 9)).pack(side="right", padx=2)
        
        tk.Button(ops_frame, text="💾 保存", command=self.save_patch_config,
                 bg="#7c3aed", fg="white", font=("Arial", 9)).pack(side="right", padx=2)
        
        tk.Button(ops_frame, text="📁 加载", command=self.load_patch_config,
                 bg="#0ea5e9", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

    def create_ai_sequence_tab(self):
        """创建AI序列生成选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🤖 AI序列生成")

        # AI参数设置
        ai_params_frame = tk.LabelFrame(frame, text="AI生成参数",
                                       font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        ai_params_frame.pack(fill="x", padx=10, pady=5)

        params_grid = tk.Frame(ai_params_frame, bg="#1e293b")
        params_grid.pack(fill="x", padx=10, pady=10)

        # 第一行参数
        row1 = tk.Frame(params_grid, bg="#1e293b")
        row1.pack(fill="x", pady=5)

        tk.Label(row1, text="创意级别:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.creativity_var = tk.StringVar(value="中等")
        creativity_combo = ttk.Combobox(row1, textvariable=self.creativity_var,
                                       values=["保守", "中等", "创新", "极致"],
                                       width=15, state="readonly")
        creativity_combo.pack(side="left", padx=5)

        tk.Label(row1, text="同步精度:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.sync_precision_var = tk.StringVar(value="高")
        sync_combo = ttk.Combobox(row1, textvariable=self.sync_precision_var,
                                 values=["低", "中", "高", "极高"],
                                 width=15, state="readonly")
        sync_combo.pack(side="left", padx=5)

        # 第二行参数
        row2 = tk.Frame(params_grid, bg="#1e293b")
        row2.pack(fill="x", pady=5)

        tk.Label(row2, text="效果强度:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.intensity_var = tk.DoubleVar(value=0.8)
        intensity_scale = tk.Scale(row2, variable=self.intensity_var, from_=0.1, to=1.0,
                                  resolution=0.1, orient="horizontal", length=200,
                                  bg="#334155", fg="white", highlightthickness=0)
        intensity_scale.pack(side="left", padx=5)

        tk.Label(row2, text="色彩丰富度:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.color_richness_var = tk.DoubleVar(value=0.9)
        color_scale = tk.Scale(row2, variable=self.color_richness_var, from_=0.1, to=1.0,
                              resolution=0.1, orient="horizontal", length=200,
                              bg="#334155", fg="white", highlightthickness=0)
        color_scale.pack(side="left", padx=5)

        # 生成按钮
        generate_btn = tk.Button(ai_params_frame, text="🤖 生成AI灯光序列",
                                command=self.generate_ai_sequence,
                                bg="#f59e0b", fg="white", font=("Arial", 14, "bold"))
        generate_btn.pack(pady=15)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(ai_params_frame, variable=self.progress_var,
                                           maximum=100, length=400)
        self.progress_bar.pack(pady=5)

        # 序列显示
        sequence_frame = tk.LabelFrame(frame, text="生成的AI序列",
                                      font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        sequence_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 序列统计
        seq_stats_frame = tk.Frame(sequence_frame, bg="#1e293b")
        seq_stats_frame.pack(fill="x", padx=5, pady=5)

        self.sequence_stats_label = tk.Label(seq_stats_frame, text="序列统计: 未生成",
                                            font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.sequence_stats_label.pack(side="left")

        # 序列操作按钮
        seq_ops_frame = tk.Frame(seq_stats_frame, bg="#1e293b")
        seq_ops_frame.pack(side="right")

        tk.Button(seq_ops_frame, text="🔄 重新生成", command=self.regenerate_sequence,
                 bg="#7c3aed", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        tk.Button(seq_ops_frame, text="💾 导出", command=self.export_sequence,
                 bg="#059669", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        tk.Button(seq_ops_frame, text="👁️ 预览", command=self.preview_sequence,
                 bg="#0ea5e9", fg="white", font=("Arial", 9)).pack(side="right", padx=2)

        # 序列表格
        seq_columns = ("时间", "灯具", "区域", "通道", "平均值", "效果", "颜色")
        self.sequence_tree = ttk.Treeview(sequence_frame, columns=seq_columns, show="headings", height=15)

        seq_widths = {"时间": 80, "灯具": 150, "区域": 100, "通道": 120, "平均值": 80, "效果": 100, "颜色": 120}
        for col in seq_columns:
            self.sequence_tree.heading(col, text=col)
            self.sequence_tree.column(col, width=seq_widths.get(col, 80))

        seq_scroll = ttk.Scrollbar(sequence_frame, orient="vertical", command=self.sequence_tree.yview)
        self.sequence_tree.configure(yscrollcommand=seq_scroll.set)

        self.sequence_tree.pack(side="left", fill="both", expand=True)
        seq_scroll.pack(side="right", fill="y")

    def create_realtime_control_tab(self):
        """创建实时控制选项卡"""
        frame = tk.Frame(self.notebook, bg="#1e293b")
        self.notebook.add(frame, text="🎮 实时控制")

        # Art-Net连接
        artnet_frame = tk.LabelFrame(frame, text="Art-Net连接到MA控台",
                                    font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        artnet_frame.pack(fill="x", padx=10, pady=5)

        conn_frame = tk.Frame(artnet_frame, bg="#1e293b")
        conn_frame.pack(fill="x", padx=10, pady=10)

        tk.Label(conn_frame, text="MA控台IP:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=5)
        self.artnet_ip_var = tk.StringVar(value="*********")
        tk.Entry(conn_frame, textvariable=self.artnet_ip_var, width=15,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        tk.Label(conn_frame, text="端口:", font=("Arial", 10), fg="white", bg="#1e293b").pack(side="left", padx=(20, 5))
        self.artnet_port_var = tk.StringVar(value="6454")
        tk.Entry(conn_frame, textvariable=self.artnet_port_var, width=10,
                font=("Arial", 10), bg="#334155", fg="white").pack(side="left", padx=5)

        self.connect_btn = tk.Button(conn_frame, text="🔗 连接", command=self.connect_artnet,
                                    bg="#059669", fg="white", font=("Arial", 10, "bold"))
        self.connect_btn.pack(side="left", padx=10)

        self.disconnect_btn = tk.Button(conn_frame, text="❌ 断开", command=self.disconnect_artnet,
                                       bg="#dc2626", fg="white", font=("Arial", 10, "bold"), state="disabled")
        self.disconnect_btn.pack(side="left", padx=5)

        # 连接状态
        self.connection_status_label = tk.Label(conn_frame, text="🔴 未连接",
                                               font=("Arial", 12, "bold"), fg="#ef4444", bg="#1e293b")
        self.connection_status_label.pack(side="left", padx=20)

        # 播放控制
        playback_frame = tk.LabelFrame(frame, text="序列播放控制",
                                      font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        playback_frame.pack(fill="x", padx=10, pady=5)

        control_frame = tk.Frame(playback_frame, bg="#1e293b")
        control_frame.pack(fill="x", padx=10, pady=10)

        self.play_btn = tk.Button(control_frame, text="▶️ 播放", command=self.start_playback,
                                 bg="#059669", fg="white", font=("Arial", 14, "bold"))
        self.play_btn.pack(side="left", padx=5)

        self.pause_btn = tk.Button(control_frame, text="⏸️ 暂停", command=self.pause_playback,
                                  bg="#f59e0b", fg="white", font=("Arial", 14, "bold"))
        self.pause_btn.pack(side="left", padx=5)

        self.stop_btn = tk.Button(control_frame, text="⏹️ 停止", command=self.stop_playback,
                                 bg="#dc2626", fg="white", font=("Arial", 14, "bold"))
        self.stop_btn.pack(side="left", padx=5)

        # 播放进度
        progress_frame = tk.Frame(playback_frame, bg="#1e293b")
        progress_frame.pack(fill="x", padx=10, pady=5)

        self.playback_progress_var = tk.DoubleVar()
        self.playback_progress = ttk.Progressbar(progress_frame, variable=self.playback_progress_var,
                                                maximum=100, length=500)
        self.playback_progress.pack(side="left", fill="x", expand=True, padx=5)

        self.time_label = tk.Label(progress_frame, text="00:00 / 00:00",
                                  font=("Arial", 10), fg="white", bg="#1e293b")
        self.time_label.pack(side="right", padx=5)

        # 实时状态监控
        status_frame = tk.LabelFrame(frame, text="实时状态监控",
                                    font=("Arial", 12, "bold"), fg="white", bg="#1e293b")
        status_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 左侧：系统状态
        left_status = tk.Frame(status_frame, bg="#1e293b")
        left_status.pack(side="left", fill="y", padx=10, pady=10)

        tk.Label(left_status, text="系统状态", font=("Arial", 12, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))

        self.current_time_label = tk.Label(left_status, text="播放时间: 00:00",
                                          font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.current_time_label.pack(anchor="w", pady=2)

        self.current_bpm_label = tk.Label(left_status, text="当前BPM: --",
                                         font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.current_bpm_label.pack(anchor="w", pady=2)

        self.sent_packets_label = tk.Label(left_status, text="发送包数: 0",
                                          font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.sent_packets_label.pack(anchor="w", pady=2)

        self.active_fixtures_label = tk.Label(left_status, text="活跃灯具: 0",
                                             font=("Arial", 10), fg="#94a3b8", bg="#1e293b")
        self.active_fixtures_label.pack(anchor="w", pady=2)

        # 右侧：活跃灯具列表
        right_status = tk.Frame(status_frame, bg="#1e293b")
        right_status.pack(side="right", fill="both", expand=True, padx=10, pady=10)

        tk.Label(right_status, text="活跃灯具", font=("Arial", 12, "bold"),
                fg="white", bg="#1e293b").pack(anchor="w", pady=(0, 10))

        active_columns = ("灯具", "Universe", "地址", "强度", "颜色", "状态")
        self.active_tree = ttk.Treeview(right_status, columns=active_columns, show="headings", height=10)

        active_widths = {"灯具": 120, "Universe": 70, "地址": 80, "强度": 60, "颜色": 100, "状态": 60}
        for col in active_columns:
            self.active_tree.heading(col, text=col)
            self.active_tree.column(col, width=active_widths.get(col, 80))

        active_scroll = ttk.Scrollbar(right_status, orient="vertical", command=self.active_tree.yview)
        self.active_tree.configure(yscrollcommand=active_scroll.set)

        self.active_tree.pack(side="left", fill="both", expand=True)
        active_scroll.pack(side="right", fill="y")

    def create_status_bar(self):
        """创建状态栏"""
        status_frame = tk.Frame(self.root, bg="#334155", height=30)
        status_frame.pack(side="bottom", fill="x")
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(status_frame, text="专业MA AI灯光系统就绪",
                                    font=("Arial", 10), fg="white", bg="#334155")
        self.status_label.pack(side="left", padx=10, pady=5)

    # ==================== 核心功能方法 ====================

    def browse_ma_xml_file(self):
        """浏览MA XML文件"""
        file_types = [
            ("MA XML文件", "*.xml"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择MA控台XML灯具库文件",
            filetypes=file_types
        )

        if file_path:
            self.xml_file_var.set(file_path)
            self.status_label.config(text=f"已选择MA XML文件: {file_path}")

    def load_sample_fixture(self, filename):
        """加载示例灯具"""
        self.xml_file_var.set(filename)
        self.parse_ma_xml()

    def parse_ma_xml(self):
        """解析MA XML灯具库"""
        xml_file = self.xml_file_var.get()
        if not xml_file:
            messagebox.showwarning("警告", "请先选择MA XML文件")
            return

        try:
            self.status_label.config(text="正在解析MA XML灯具库...")

            # 解析XML文件
            if self.ma_parser.parse_ma_xml(xml_file):
                self.update_fixture_library_display()
                self.update_fixture_combo()

                stats = self.ma_parser.get_statistics()
                self.status_label.config(text=f"成功解析 {stats['total_fixtures']} 个MA灯具类型")

                messagebox.showinfo("成功",
                                   f"成功解析MA XML灯具库！\n\n"
                                   f"总灯具数: {stats['total_fixtures']}\n"
                                   f"制造商数: {stats['manufacturer_count']}\n"
                                   f"制造商: {', '.join(stats['manufacturers'][:5])}{'...' if len(stats['manufacturers']) > 5 else ''}")
            else:
                messagebox.showerror("错误", "MA XML文件解析失败")

        except Exception as e:
            messagebox.showerror("错误", f"解析过程出错: {e}")

    def update_fixture_library_display(self):
        """更新灯具库显示"""
        # 清除现有数据
        for item in self.fixture_tree.get_children():
            self.fixture_tree.delete(item)

        # 添加灯具数据
        for fixture_id, fixture in self.ma_parser.fixtures.items():
            # 获取功率信息
            power = fixture['physical'].get('power', '--')
            if isinstance(power, (int, float)):
                power_str = f"{power}W"
            else:
                power_str = str(power)

            self.fixture_tree.insert("", "end", values=(
                fixture['manufacturer'],
                fixture['name'],
                fixture['short_name'],
                fixture['channel_count'],
                len(fixture['modes']),
                power_str
            ))

        # 更新制造商下拉框
        manufacturers = ["全部"] + list(set(f['manufacturer'] for f in self.ma_parser.fixtures.values()))
        self.manufacturer_combo['values'] = manufacturers

        # 更新统计
        stats = self.ma_parser.get_statistics()
        self.stats_label.config(text=f"MA灯具库统计: {stats['total_fixtures']}个灯具, {stats['manufacturer_count']}个制造商")

    def update_fixture_combo(self):
        """更新灯具选择下拉框"""
        fixture_options = []
        for fixture_id, fixture in self.ma_parser.fixtures.items():
            option = f"{fixture['manufacturer']} - {fixture['name']}"
            fixture_options.append(option)

        self.fixture_combo['values'] = fixture_options
        if fixture_options:
            self.fixture_combo.set(fixture_options[0])
            self.on_fixture_selection_change()

    def on_fixture_selection_change(self, event=None):
        """灯具选择改变时的处理"""
        selected = self.selected_fixture_var.get()
        if not selected:
            return

        # 解析选择的灯具
        try:
            manufacturer, fixture_name = selected.split(" - ", 1)

            # 查找灯具定义
            fixture_def = None
            for f in self.ma_parser.fixtures.values():
                if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                    fixture_def = f
                    break

            if fixture_def:
                # 更新模式下拉框
                mode_options = list(fixture_def['modes'].keys())
                self.mode_combo['values'] = mode_options
                if mode_options:
                    self.mode_combo.set(mode_options[0])

        except Exception as e:
            print(f"灯具选择处理错误: {e}")

    def on_search_change(self, event=None):
        """搜索改变时的处理"""
        self.filter_fixture_display()

    def on_manufacturer_change(self, event=None):
        """制造商筛选改变时的处理"""
        self.filter_fixture_display()

    def filter_fixture_display(self):
        """筛选灯具显示"""
        search_text = self.search_var.get().lower()
        selected_manufacturer = self.manufacturer_var.get()

        # 清除现有显示
        for item in self.fixture_tree.get_children():
            self.fixture_tree.delete(item)

        # 重新添加符合条件的灯具
        for fixture_id, fixture in self.ma_parser.fixtures.items():
            # 制造商筛选
            if selected_manufacturer != "全部" and fixture['manufacturer'] != selected_manufacturer:
                continue

            # 搜索筛选
            if search_text and search_text not in fixture['name'].lower() and search_text not in fixture['short_name'].lower():
                continue

            # 添加到显示
            power = fixture['physical'].get('power', '--')
            if isinstance(power, (int, float)):
                power_str = f"{power}W"
            else:
                power_str = str(power)

            self.fixture_tree.insert("", "end", values=(
                fixture['manufacturer'],
                fixture['name'],
                fixture['short_name'],
                fixture['channel_count'],
                len(fixture['modes']),
                power_str
            ))

    def show_fixture_details(self, event=None):
        """显示灯具详情"""
        selection = self.fixture_tree.selection()
        if not selection:
            return

        item = self.fixture_tree.item(selection[0])
        values = item['values']

        if len(values) >= 3:
            manufacturer = values[0]
            fixture_name = values[1]

            # 查找灯具定义
            fixture_def = None
            for f in self.ma_parser.fixtures.values():
                if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                    fixture_def = f
                    break

            if fixture_def:
                # 获取详细信息
                summary = self.ma_parser.get_fixture_summary(fixture_def['id'])

                # 显示详情窗口
                detail_window = tk.Toplevel(self.root)
                detail_window.title(f"灯具详情 - {manufacturer} {fixture_name}")
                detail_window.geometry("800x600")
                detail_window.configure(bg="#1e293b")

                text_widget = tk.Text(detail_window, font=("Consolas", 10),
                                     bg="#334155", fg="white", wrap="word")
                scrollbar = ttk.Scrollbar(detail_window, orient="vertical", command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)

                text_widget.insert("1.0", summary)
                text_widget.config(state="disabled")

                text_widget.pack(side="left", fill="both", expand=True, padx=10, pady=10)
                scrollbar.pack(side="right", fill="y", pady=10)

    def export_fixtures_json(self):
        """导出灯具为JSON"""
        if not self.ma_parser.fixtures:
            messagebox.showwarning("警告", "没有可导出的灯具数据")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出MA灯具库为JSON",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            if self.ma_parser.export_to_json(file_path):
                messagebox.showinfo("成功", f"MA灯具库已导出到: {file_path}")
            else:
                messagebox.showerror("错误", "导出失败")

    def browse_music_file(self):
        """浏览音乐文件"""
        file_types = [
            ("音频文件", "*.mp3 *.wav *.flac *.m4a *.aac"),
            ("MP3文件", "*.mp3"),
            ("WAV文件", "*.wav"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择音乐文件",
            filetypes=file_types
        )

        if file_path:
            self.music_file_var.set(file_path)
            self.status_label.config(text=f"已选择音乐文件: {file_path}")

    def analyze_music(self):
        """分析音乐"""
        music_file = self.music_file_var.get()
        if not music_file:
            messagebox.showwarning("警告", "请先选择音乐文件")
            return

        # 在新线程中执行分析
        analysis_thread = threading.Thread(target=self._analyze_music_thread, args=(music_file,), daemon=True)
        analysis_thread.start()

    def _analyze_music_thread(self, music_file):
        """音乐分析线程"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="AI正在分析音乐..."))

            # 使用简单音乐分析器
            analysis_result = self.music_analyzer.analyze_music(music_file)

            # 保存分析结果
            self.music_analysis = analysis_result
            self.current_music_file = music_file
            self.music_loaded = True

            # 更新界面显示
            self.root.after(0, self.update_music_analysis_display)
            self.root.after(0, lambda: self.status_label.config(text="智能音乐分析完成"))

        except Exception as e:
            print(f"❌ 音乐分析失败: {e}")
            # 使用简单分析作为回退
            self.root.after(0, lambda: self._simple_music_analysis(music_file))

    def _simple_music_analysis(self, music_file):
        """简单音乐分析（回退方案）"""
        try:
            print("🔄 使用简单分析模式...")
            self.status_label.config(text="使用简单分析模式...")

            import os
            file_size = os.path.getsize(music_file) / 1024 / 1024  # MB

            # 根据文件大小估算时长（粗略估算）
            estimated_duration = file_size * 10  # 假设1MB约10秒
            estimated_duration = min(max(estimated_duration, 30), 300)  # 限制在30-300秒

            # 生成基础分析数据
            bpm = random.uniform(100, 140)

            # 生成节拍时间点
            beat_interval = 60 / bpm
            beat_times = []
            current_time = 0
            while current_time < estimated_duration:
                beat_times.append(current_time)
                current_time += beat_interval

            # 生成能量分布
            energy_profile = []
            for i in range(int(estimated_duration / 2)):
                energy = random.uniform(0.4, 0.9)
                energy_profile.append(energy)

            # 根据文件名猜测风格
            filename = os.path.basename(music_file).lower()
            if any(word in filename for word in ['rock', '摇滚', 'metal']):
                style, mood = "快板/摇滚", "动感/激昂"
                bpm = random.uniform(120, 160)
            elif any(word in filename for word in ['dance', '舞曲', 'edm', 'electronic']):
                style, mood = "电子/舞曲", "激昂/兴奋"
                bpm = random.uniform(128, 150)
            elif any(word in filename for word in ['ballad', '抒情', 'slow']):
                style, mood = "慢板/抒情", "平静/舒缓"
                bpm = random.uniform(70, 100)
            else:
                style, mood = "中板/流行", "稳定/愉悦"
                bpm = random.uniform(100, 130)

            # 保存音乐文件路径用于播放
            self.current_music_file = music_file
            self.music_loaded = True

            self.music_analysis = {
                'file_path': music_file,
                'bpm': float(bpm),
                'duration': float(estimated_duration),
                'beat_times': beat_times,
                'energy_profile': energy_profile,
                'style': style,
                'mood': mood,
                'spectral_features': {
                    'avg_spectral_centroid': 2500.0,
                    'avg_spectral_rolloff': 5000.0,
                    'avg_energy': 0.1
                },
                'analysis_time': datetime.now().isoformat(),
                'analysis_method': 'simple'
            }

            # 更新界面显示
            self.update_music_analysis_display()
            self.status_label.config(text="简单音乐分析完成")

            print("✅ 简单音乐分析完成")

        except Exception as e:
            messagebox.showerror("错误", f"音乐分析失败: {e}")
            print(f"❌ 简单分析也失败: {e}")

    def update_music_analysis_display(self):
        """更新音乐分析显示"""
        if not self.music_analysis:
            return

        analysis = self.music_analysis

        # 更新基本信息标签
        self.bpm_label.config(text=f"BPM: {analysis['bpm']:.1f}")
        self.duration_label.config(text=f"时长: {analysis['duration']:.1f}秒")
        self.style_label.config(text=f"风格: {analysis['style']}")
        self.mood_label.config(text=f"情绪: {analysis['mood']}")
        self.beats_label.config(text=f"节拍数: {len(analysis['beat_times'])}")

        # 更新详细分析文本
        detail_text = f"""🎵 AI音乐分析详细报告
{'='*50}

📁 文件信息:
  文件路径: {analysis['file_path']}
  分析时间: {analysis['analysis_time']}

🎼 音乐特征:
  BPM (每分钟节拍): {analysis['bpm']:.2f}
  总时长: {analysis['duration']:.2f} 秒
  总节拍数: {len(analysis['beat_times'])}
  音乐风格: {analysis['style']}
  情绪特征: {analysis['mood']}

📊 能量分布:
  能量段数: {len(analysis['energy_profile'])}
  平均能量: {sum(analysis['energy_profile'])/len(analysis['energy_profile']):.3f}
  最高能量: {max(analysis['energy_profile']):.3f}
  最低能量: {min(analysis['energy_profile']):.3f}

⏱️ 节拍时间点 (前20个):
"""

        for i, beat_time in enumerate(analysis['beat_times'][:20]):
            detail_text += f"  节拍 {i+1:2d}: {beat_time:7.3f}s\n"

        if len(analysis['beat_times']) > 20:
            detail_text += f"  ... 还有 {len(analysis['beat_times']) - 20} 个节拍\n"

        detail_text += f"\n📈 能量分布 (每10秒):\n"
        for i, energy in enumerate(analysis['energy_profile']):
            time_start = i * 10
            time_end = (i + 1) * 10
            bar_length = int(energy * 30)
            bar = "█" * bar_length + "░" * (30 - bar_length)
            detail_text += f"  {time_start:3d}-{time_end:3d}s: {bar} {energy:.3f}\n"

        self.analysis_text.delete("1.0", "end")
        self.analysis_text.insert("1.0", detail_text)

    def add_fixture_patch(self):
        """添加灯具配接"""
        if not self.selected_fixture_var.get():
            messagebox.showwarning("警告", "请先选择MA灯具类型")
            return

        try:
            # 解析选择的灯具
            selected = self.selected_fixture_var.get()
            manufacturer, fixture_name = selected.split(" - ", 1)

            # 查找灯具定义
            fixture_def = None
            for f in self.ma_parser.fixtures.values():
                if f['manufacturer'] == manufacturer and f['name'] == fixture_name:
                    fixture_def = f
                    break

            if not fixture_def:
                messagebox.showerror("错误", "找不到MA灯具定义")
                return

            # 获取参数
            start_address = int(self.start_address_var.get())
            universe = int(self.universe_var.get())
            count = int(self.count_var.get())
            zone = self.zone_var.get()
            mode_name = self.mode_var.get()

            if not mode_name:
                messagebox.showwarning("警告", "请选择灯具模式")
                return

            # 获取模式信息
            mode_info = fixture_def['modes'].get(mode_name)
            if not mode_info:
                messagebox.showerror("错误", "找不到指定的灯具模式")
                return

            channel_count = mode_info['channel_count']

            # 检查地址冲突
            for i in range(count):
                current_start = start_address + i * channel_count
                current_end = current_start + channel_count - 1

                for existing in self.configured_fixtures:
                    if existing['universe'] == universe:
                        existing_start = existing['start_address']
                        existing_end = existing_start + existing['channel_count'] - 1

                        if not (current_end < existing_start or current_start > existing_end):
                            messagebox.showerror("错误",
                                               f"地址冲突！灯具 {i+1} 的地址 {current_start}-{current_end} "
                                               f"与现有灯具 {existing['name']} 的地址 {existing_start}-{existing_end} 重叠")
                            return

            # 添加多个灯具
            for i in range(count):
                fixture_config = {
                    'id': len(self.configured_fixtures) + 1,
                    'name': f"{fixture_def['short_name']}_{i+1}",
                    'ma_type': fixture_def['id'],
                    'manufacturer': manufacturer,
                    'fixture_name': fixture_name,
                    'mode': mode_name,
                    'start_address': start_address + i * channel_count,
                    'universe': universe,
                    'zone': zone,
                    'channel_count': channel_count,
                    'channels': mode_info['channels'],
                    'fixture_def': fixture_def
                }
                self.configured_fixtures.append(fixture_config)

            # 更新显示
            self.update_patch_display()

            # 自动递增地址
            next_address = start_address + count * channel_count
            self.start_address_var.set(str(next_address))

            self.status_label.config(text=f"已添加 {count} 个 {fixture_name} ({mode_name}模式)")

        except ValueError as e:
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            messagebox.showerror("错误", f"添加灯具配接失败: {e}")

    def update_patch_display(self):
        """更新配接显示"""
        # 清除现有数据
        for item in self.patch_tree.get_children():
            self.patch_tree.delete(item)

        # 添加配接数据
        for fixture in self.configured_fixtures:
            address_range = f"{fixture['start_address']}-{fixture['start_address'] + fixture['channel_count'] - 1}"
            ma_type = f"{fixture['manufacturer']} {fixture['fixture_name']}"

            self.patch_tree.insert("", "end", values=(
                fixture['id'],
                fixture['name'],
                ma_type,
                fixture['mode'],
                address_range,
                fixture['universe'],
                fixture['zone'],
                fixture['channel_count']
            ))

        # 更新统计
        total_fixtures = len(self.configured_fixtures)
        total_channels = sum(f['channel_count'] for f in self.configured_fixtures)
        universes = len(set(f['universe'] for f in self.configured_fixtures))

        self.patch_stats_label.config(text=f"配接统计: {total_fixtures}个灯具, {total_channels}个通道, {universes}个Universe")

    def clear_all_patches(self):
        """清空所有配接"""
        if messagebox.askyesno("确认", "确定要清空所有灯具配接吗？"):
            self.configured_fixtures.clear()
            self.update_patch_display()
            self.status_label.config(text="已清空所有灯具配接")

    def save_patch_config(self):
        """保存配接配置"""
        if not self.configured_fixtures:
            messagebox.showwarning("警告", "没有可保存的配接配置")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存灯具配接配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                config_data = {
                    'configured_fixtures': self.configured_fixtures,
                    'save_time': datetime.now().isoformat(),
                    'version': '1.0'
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"配接配置已保存到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_patch_config(self):
        """加载配接配置"""
        file_path = filedialog.askopenfilename(
            title="加载灯具配接配置",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                self.configured_fixtures = config_data.get('configured_fixtures', [])
                self.update_patch_display()

                messagebox.showinfo("成功", f"配接配置已加载: {len(self.configured_fixtures)}个灯具")
                self.status_label.config(text=f"已加载配接配置: {len(self.configured_fixtures)}个灯具")

            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {e}")

    def generate_ai_sequence(self):
        """生成AI灯光序列"""
        if not self.music_analysis:
            messagebox.showwarning("警告", "请先完成音乐AI分析")
            return

        if not self.configured_fixtures:
            messagebox.showwarning("警告", "请先配接MA灯具")
            return

        # 在新线程中生成序列
        generate_thread = threading.Thread(target=self._generate_ai_sequence_thread, daemon=True)
        generate_thread.start()

    def _generate_ai_sequence_thread(self):
        """AI序列生成线程"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="AI正在生成专业灯光序列..."))
            self.root.after(0, lambda: self.progress_var.set(0))

            analysis = self.music_analysis
            beat_times = analysis['beat_times']
            energy_profile = analysis['energy_profile']
            style = analysis['style']
            mood = analysis['mood']

            # 获取AI参数
            creativity = self.creativity_var.get()
            sync_precision = self.sync_precision_var.get()
            intensity_factor = self.intensity_var.get()
            color_richness = self.color_richness_var.get()

            self.lighting_sequence = []
            total_beats = len(beat_times)

            # 根据风格和情绪确定基础效果
            base_effects = self._get_ai_base_effects(style, mood, creativity)

            # 为每个节拍生成灯光事件
            for i, beat_time in enumerate(beat_times):
                # 更新进度
                progress = (i / total_beats) * 100
                self.root.after(0, lambda p=progress: self.progress_var.set(p))

                # 计算当前能量级别
                energy_index = min(int(beat_time / 10), len(energy_profile) - 1)
                current_energy = energy_profile[energy_index] * intensity_factor

                # 根据同步精度决定是否在此节拍生成事件
                sync_factor = {"低": 4, "中": 2, "高": 1, "极高": 0.5}[sync_precision]
                if i % max(1, int(sync_factor)) == 0:

                    # 为每个配接的灯具生成事件
                    for fixture in self.configured_fixtures:
                        events = self._generate_fixture_events(
                            fixture, beat_time, current_energy, base_effects,
                            i, color_richness
                        )
                        self.lighting_sequence.extend(events)

                # 模拟处理时间
                if i % 20 == 0:
                    time.sleep(0.01)

            # 排序序列
            self.lighting_sequence.sort(key=lambda x: x['time'])

            # 更新界面显示
            self.root.after(0, self.update_sequence_display)
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_label.config(text=f"AI序列生成完成，共 {len(self.lighting_sequence)} 个专业事件"))

            # 延迟重置进度条
            self.root.after(3000, lambda: self.progress_var.set(0))

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"AI序列生成失败: {e}"))

    def _get_ai_base_effects(self, style, mood, creativity):
        """获取AI基础效果"""
        effects = {
            "慢板/抒情": {
                "colors": ["#FFE4B5", "#DDA0DD", "#87CEEB", "#F0E68C"],  # 暖色调
                "intensity_range": [0.3, 0.7],
                "change_frequency": 4,
                "movement_speed": "慢"
            },
            "中板/流行": {
                "colors": ["#FF6347", "#32CD32", "#1E90FF", "#FFD700"],  # 鲜艳色
                "intensity_range": [0.5, 0.8],
                "change_frequency": 2,
                "movement_speed": "中"
            },
            "快板/摇滚": {
                "colors": ["#FF0000", "#FFFF00", "#FFFFFF", "#FF4500"],  # 强烈对比
                "intensity_range": [0.7, 1.0],
                "change_frequency": 1,
                "movement_speed": "快"
            },
            "电子/舞曲": {
                "colors": ["#FF00FF", "#00FFFF", "#00FF00", "#FF1493"],  # 霓虹色
                "intensity_range": [0.8, 1.0],
                "change_frequency": 0.5,
                "movement_speed": "极快"
            }
        }

        base = effects.get(style, effects["中板/流行"])

        # 根据创意级别调整
        if creativity == "创新":
            base["colors"].extend(["#00CED1", "#FF8C00", "#FF69B4"])
            base["change_frequency"] *= 0.8
        elif creativity == "极致":
            base["colors"].extend(["#00CED1", "#FF8C00", "#FF69B4", "#32CD32", "#191970"])
            base["change_frequency"] *= 0.6

        return base

    def _generate_fixture_events(self, fixture, beat_time, energy, base_effects, beat_index, color_richness):
        """为单个灯具生成事件"""
        events = []

        # 基础强度
        min_intensity, max_intensity = base_effects["intensity_range"]
        intensity = min_intensity + (max_intensity - min_intensity) * energy

        # 生成DMX值
        dmx_values = {}
        channels = fixture['channels']

        # 调光通道 - 必须255才能出光
        if 'dimmer' in channels:
            dmx_values['dimmer'] = 255  # 全亮度确保出光

        # 快门/频闪通道 - 必须255才能出光
        if 'shutter_n_strobe' in channels:
            if energy > 0.8:
                dmx_values['shutter_n_strobe'] = random.randint(64, 95)  # 高能量时频闪
            else:
                dmx_values['shutter_n_strobe'] = 255  # 正常出光

        # RGB颜色通道 - 根据音乐特征生成
        if all(ch in channels for ch in ['red', 'green', 'blue']):
            color_values = self._generate_music_responsive_colors(base_effects["colors"], beat_index, color_richness, intensity, energy, beat_time)
            dmx_values.update(color_values)

        # 白光通道 - 增强亮度
        if 'white' in channels:
            dmx_values['white'] = int(150 * intensity * color_richness)

        # 琥珀和UV通道 - 增强效果
        if 'amber' in channels:
            dmx_values['amber'] = int(120 * intensity * color_richness)
        if 'uv' in channels:
            dmx_values['uv'] = int(100 * intensity * color_richness)

        # 移动通道（摇头灯）- 必须有动作
        if 'pan' in channels:
            movement_values = self._generate_movement_values(fixture, beat_time, energy, base_effects["movement_speed"])
            dmx_values.update(movement_values)

        # 色轮通道
        if 'colorwheel1' in channels:
            dmx_values['colorwheel1'] = self._generate_colorwheel_value(beat_index, energy)

        # 图案轮通道
        if 'gobo1' in channels:
            dmx_values['gobo1'] = self._generate_gobo_value(beat_index, energy)

        # 图案轮旋转
        if 'gobo1rot' in channels:
            dmx_values['gobo1rot'] = self._generate_gobo_rotation_value(energy, beat_time)

        # 第二个图案轮
        if 'gobo2' in channels:
            dmx_values['gobo2'] = self._generate_gobo_value(beat_index + 1, energy * 0.8)

        # 棱镜效果
        if 'prism1' in channels:
            dmx_values['prism1'] = self._generate_prism_value(energy)

        if 'prism1rot' in channels:
            dmx_values['prism1rot'] = self._generate_prism_rotation_value(energy, beat_time)

        # 光束控制
        if 'focus1' in channels:
            dmx_values['focus1'] = self._generate_focus_value(energy)

        if 'zoom' in channels:
            dmx_values['zoom'] = self._generate_zoom_value(energy, beat_index)

        if 'iris1' in channels:
            dmx_values['iris1'] = self._generate_iris_value(energy)

        # 雾化效果
        if 'frost1' in channels:
            dmx_values['frost1'] = self._generate_frost_value(energy)

        # 控制通道
        if 'control1' in channels:
            dmx_values['control1'] = 0  # 正常模式

        print(f"生成灯具事件 {fixture['name']}: {len(dmx_values)}个通道, 能量={energy:.2f}")
        print(f"  DMX值: {dmx_values}")

        # 创建事件
        if dmx_values:
            event = {
                'time': beat_time,
                'fixture_id': fixture['id'],
                'fixture_name': fixture['name'],
                'universe': fixture['universe'],
                'start_address': fixture['start_address'],
                'dmx_values': dmx_values,
                'energy_level': energy,
                'zone': fixture['zone'],
                'ma_type': fixture['ma_type']
            }
            events.append(event)

        return events

    def _generate_color_values(self, color_palette, beat_index, color_richness, intensity):
        """生成颜色值"""
        # 选择颜色
        color_hex = color_palette[beat_index % len(color_palette)]

        # 转换为RGB
        rgb = self._hex_to_rgb(color_hex)

        return {
            'red': int(rgb[0] * intensity * color_richness),
            'green': int(rgb[1] * intensity * color_richness),
            'blue': int(rgb[2] * intensity * color_richness)
        }

    def _generate_enhanced_color_values(self, color_palette, beat_index, color_richness, intensity, energy):
        """生成增强的颜色值"""
        # 选择颜色
        color_hex = color_palette[beat_index % len(color_palette)]
        rgb = self._hex_to_rgb(color_hex)

        # 根据能量调整颜色强度
        energy_boost = 1.0 + (energy * 0.5)  # 能量越高，颜色越鲜艳

        return {
            'red': max(0, min(255, int(rgb[0] * intensity * color_richness * energy_boost * 255))),
            'green': max(0, min(255, int(rgb[1] * intensity * color_richness * energy_boost * 255))),
            'blue': max(0, min(255, int(rgb[2] * intensity * color_richness * energy_boost * 255)))
        }

    def _generate_music_responsive_colors(self, color_palette, beat_index, color_richness, intensity, energy, beat_time):
        """根据音乐特征生成响应式颜色"""
        # 获取音乐分析数据
        if not self.music_analysis:
            return self._generate_enhanced_color_values(color_palette, beat_index, color_richness, intensity, energy)

        bpm = self.music_analysis.get('bpm', 120)
        spectral_features = self.music_analysis.get('spectral_features', {})
        avg_spectral_centroid = spectral_features.get('avg_spectral_centroid', 2000)

        # 根据频谱重心调整颜色温度
        # 高频内容多 -> 冷色调 (蓝、青)
        # 低频内容多 -> 暖色调 (红、橙)
        if avg_spectral_centroid > 3000:  # 高频丰富
            color_temp_colors = ["#00FFFF", "#0080FF", "#4080FF", "#8080FF"]  # 冷色调
        elif avg_spectral_centroid > 2000:  # 中频
            color_temp_colors = ["#FF8000", "#FFFF00", "#80FF80", "#FF80FF"]  # 中性色调
        else:  # 低频丰富
            color_temp_colors = ["#FF4000", "#FF8000", "#FFFF00", "#FF0080"]  # 暖色调

        # 根据BPM调整颜色变化速度
        if bpm > 140:  # 快节奏 - 快速变化
            color_index = int(beat_time * 2) % len(color_temp_colors)
        elif bpm > 100:  # 中等节奏
            color_index = int(beat_time) % len(color_temp_colors)
        else:  # 慢节奏 - 缓慢变化
            color_index = int(beat_time / 2) % len(color_temp_colors)

        # 选择基础颜色
        base_color = color_temp_colors[color_index]
        rgb = self._hex_to_rgb(base_color)

        # 根据能量调整颜色饱和度和亮度
        saturation_boost = 0.5 + (energy * 0.5)  # 能量高时饱和度高
        brightness_boost = 0.3 + (energy * 0.7)  # 能量高时亮度高

        # 添加音乐节拍的颜色脉冲效果
        beat_pulse = 1.0 + (0.3 * energy * np.sin(beat_time * bpm / 60 * 2 * np.pi))

        return {
            'red': max(0, min(255, int(rgb[0] * saturation_boost * brightness_boost * beat_pulse * color_richness * 255))),
            'green': max(0, min(255, int(rgb[1] * saturation_boost * brightness_boost * beat_pulse * color_richness * 255))),
            'blue': max(0, min(255, int(rgb[2] * saturation_boost * brightness_boost * beat_pulse * color_richness * 255)))
        }

    def _generate_gobo_rotation_value(self, energy, beat_time):
        """生成图案轮旋转值"""
        if energy > 0.6:
            # 高能量时快速旋转
            return int(128 + (energy * 60))  # 128-188范围，正向旋转
        elif energy > 0.3:
            # 中等能量时慢速旋转
            return int(128 + (energy * 30))  # 128-158范围
        else:
            return 0  # 静止

    def _generate_prism_value(self, energy):
        """生成棱镜值"""
        if energy > 0.7:
            return 200  # 启用棱镜
        else:
            return 0    # 关闭棱镜

    def _generate_prism_rotation_value(self, energy, beat_time):
        """生成棱镜旋转值"""
        if energy > 0.7:
            return int(128 + (energy * 80))  # 快速旋转
        else:
            return 0

    def _generate_focus_value(self, energy):
        """生成聚焦值"""
        # 能量高时聚焦，能量低时散焦
        return int((1 - energy) * 255)

    def _generate_zoom_value(self, energy, beat_index):
        """生成变焦值"""
        # 根据能量和节拍变化光束角度
        base_zoom = int(energy * 255)
        variation = int(30 * (1 if beat_index % 2 == 0 else -1))
        return max(0, min(255, base_zoom + variation))

    def _generate_iris_value(self, energy):
        """生成光圈值"""
        # 能量高时光圈开大，能量低时收小
        return int(energy * 191)  # 0-191范围

    def _generate_frost_value(self, energy):
        """生成雾化值"""
        if energy < 0.3:
            return int(energy * 255)  # 低能量时轻微雾化
        else:
            return 0  # 高能量时清晰光束

    def _hex_to_rgb(self, hex_color):
        """十六进制颜色转RGB"""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))

    def _generate_movement_values(self, fixture, beat_time, energy, speed):
        """生成移动值"""
        speed_factor = {"慢": 0.3, "中": 0.6, "快": 0.8, "极快": 1.0}[speed]

        # 基于时间和能量的摆动
        pan_base = 127 + int(100 * energy * speed_factor * (1 if int(beat_time) % 2 == 0 else -1))
        tilt_base = 127 + int(50 * energy * speed_factor * (1 if int(beat_time / 2) % 2 == 0 else -1))

        movement_values = {
            'pan': max(0, min(255, pan_base)),
            'tilt': max(0, min(255, tilt_base))
        }

        # 16位精度支持
        if 'pan_fine' in fixture['channels']:
            movement_values['pan_fine'] = random.randint(0, 255)
        if 'tilt_fine' in fixture['channels']:
            movement_values['tilt_fine'] = random.randint(0, 255)

        return movement_values

    def _generate_colorwheel_value(self, beat_index, energy):
        """生成色轮值"""
        if energy > 0.7:
            # 高能量时使用彩色
            colors = [10, 20, 30, 40, 50, 60, 70]  # 不同颜色位置
            return colors[beat_index % len(colors)]
        else:
            return 0  # 开放/白光

    def _generate_gobo_value(self, beat_index, energy):
        """生成图案轮值"""
        if energy > 0.6:
            # 高能量时使用图案
            gobos = [10, 20, 30, 40, 50, 60]  # 不同图案位置
            return gobos[beat_index % len(gobos)]
        else:
            return 0  # 开放

    def _generate_strobe_value(self, energy, change_frequency):
        """生成频闪值"""
        if energy > 0.8 and change_frequency < 1:
            return random.randint(64, 95)  # 高速频闪
        elif energy > 0.6:
            return random.randint(32, 63)  # 中速频闪
        else:
            return 255  # 开启快门（正确值应该是255）

    def update_sequence_display(self):
        """更新序列显示"""
        # 清除现有数据
        for item in self.sequence_tree.get_children():
            self.sequence_tree.delete(item)

        # 显示前100个事件
        display_count = min(100, len(self.lighting_sequence))

        for i, event in enumerate(self.lighting_sequence[:display_count]):
            time_str = f"{event['time']:.2f}s"
            fixture_name = event['fixture_name']
            zone = event['zone']

            # 格式化通道信息
            channels_str = ", ".join(event['dmx_values'].keys())

            # 计算平均值
            avg_value = sum(event['dmx_values'].values()) / len(event['dmx_values'])
            value_str = f"{avg_value:.0f}"

            # 效果描述
            effect_str = "移动" if any(ch in event['dmx_values'] for ch in ['pan', 'tilt']) else "静态"
            if 'shutter_n_strobe' in event['dmx_values'] and event['dmx_values']['shutter_n_strobe'] < 200:
                effect_str += "+频闪"

            # 颜色描述
            if all(ch in event['dmx_values'] for ch in ['red', 'green', 'blue']):
                r, g, b = event['dmx_values']['red'], event['dmx_values']['green'], event['dmx_values']['blue']
                color_str = f"RGB({r},{g},{b})"
            else:
                color_str = "单色"

            self.sequence_tree.insert("", "end", values=(
                time_str, fixture_name, zone, channels_str, value_str, effect_str, color_str
            ))

        # 更新统计
        total_events = len(self.lighting_sequence)
        duration = self.music_analysis['duration'] if self.music_analysis else 0

        self.sequence_stats_label.config(text=f"AI序列统计: {total_events}个事件, 时长{duration:.1f}秒, 显示前{display_count}个")

    def regenerate_sequence(self):
        """重新生成序列"""
        if messagebox.askyesno("确认", "确定要重新生成AI序列吗？当前序列将被覆盖。"):
            self.generate_ai_sequence()

    def preview_sequence(self):
        """预览序列"""
        if not self.lighting_sequence:
            messagebox.showwarning("警告", "没有可预览的序列")
            return

        # 创建预览窗口
        preview_window = tk.Toplevel(self.root)
        preview_window.title("AI序列预览")
        preview_window.geometry("600x400")
        preview_window.configure(bg="#1e293b")

        preview_text = tk.Text(preview_window, font=("Consolas", 9),
                              bg="#334155", fg="white", wrap="word")
        preview_scroll = ttk.Scrollbar(preview_window, orient="vertical", command=preview_text.yview)
        preview_text.configure(yscrollcommand=preview_scroll.set)

        # 生成预览内容
        preview_content = f"""🤖 AI灯光序列预览
{'='*50}

📊 序列统计:
  总事件数: {len(self.lighting_sequence)}
  音乐时长: {self.music_analysis['duration']:.1f}秒
  音乐风格: {self.music_analysis['style']}
  情绪特征: {self.music_analysis['mood']}

🎭 灯具分布:
"""

        # 统计各区域灯具
        zone_stats = {}
        for event in self.lighting_sequence:
            zone = event['zone']
            zone_stats[zone] = zone_stats.get(zone, 0) + 1

        for zone, count in zone_stats.items():
            preview_content += f"  {zone}: {count}个事件\n"

        preview_content += f"\n⏱️ 时间轴预览 (前20个事件):\n"
        preview_content += "-" * 50 + "\n"

        for i, event in enumerate(self.lighting_sequence[:20]):
            preview_content += f"{event['time']:6.2f}s | {event['fixture_name']} | {event['zone']}\n"
            dmx_str = ", ".join([f"{ch}:{val}" for ch, val in list(event['dmx_values'].items())[:3]])
            preview_content += f"         DMX: {dmx_str}...\n\n"

        if len(self.lighting_sequence) > 20:
            preview_content += f"... 还有 {len(self.lighting_sequence) - 20} 个事件\n"

        preview_text.insert("1.0", preview_content)
        preview_text.config(state="disabled")

        preview_text.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        preview_scroll.pack(side="right", fill="y", pady=10)

    def export_sequence(self):
        """导出序列"""
        if not self.lighting_sequence:
            messagebox.showwarning("警告", "没有可导出的序列")
            return

        file_path = filedialog.asksaveasfilename(
            title="导出AI灯光序列",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("MA2 Show文件", "*.xml"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                export_data = {
                    'music_analysis': self.music_analysis,
                    'configured_fixtures': self.configured_fixtures,
                    'lighting_sequence': self.lighting_sequence,
                    'ai_parameters': {
                        'creativity': self.creativity_var.get(),
                        'sync_precision': self.sync_precision_var.get(),
                        'intensity_factor': self.intensity_var.get(),
                        'color_richness': self.color_richness_var.get()
                    },
                    'export_time': datetime.now().isoformat(),
                    'version': '1.0'
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                messagebox.showinfo("成功", f"AI序列已导出到: {file_path}")

            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")

    def connect_artnet(self):
        """连接Art-Net"""
        try:
            target_ip = self.artnet_ip_var.get()
            target_port = int(self.artnet_port_var.get())

            # 获取本地网络信息
            import socket as sock
            hostname = sock.gethostname()
            local_ip = sock.gethostbyname(hostname)

            print(f"🌐 Art-Net连接信息:")
            print(f"   本地主机: {hostname}")
            print(f"   本地IP: {local_ip}")
            print(f"   目标IP: {target_ip}")
            print(f"   目标端口: {target_port}")

            # 创建UDP socket
            self.artnet_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.artnet_socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)

            # 绑定到本地端口（可选）
            try:
                self.artnet_socket.bind(('', 0))  # 绑定到任意可用端口
                local_port = self.artnet_socket.getsockname()[1]
                print(f"   本地端口: {local_port}")
            except Exception as e:
                print(f"   绑定警告: {e}")

            # 测试连接
            print(f"📦 发送测试Art-Net包...")
            test_packet = self._create_artnet_packet(1, [0] * 512)
            bytes_sent = self.artnet_socket.sendto(test_packet, (target_ip, target_port))
            print(f"✅ 测试包已发送: {bytes_sent} 字节到 {target_ip}:{target_port}")

            self.connection_status_label.config(text="🟢 已连接", fg="#10b981")
            self.status_label.config(text=f"Art-Net已连接: {local_ip} -> {target_ip}:{target_port}")

            self.connect_btn.config(state="disabled")
            self.disconnect_btn.config(state="normal")

            messagebox.showinfo("成功", f"Art-Net连接成功！\n本地: {local_ip}\n目标: {target_ip}:{target_port}")

        except Exception as e:
            print(f"❌ Art-Net连接错误: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"Art-Net连接失败: {e}")
            self.connection_status_label.config(text="🔴 连接失败", fg="#ef4444")

    def disconnect_artnet(self):
        """断开Art-Net连接"""
        if self.artnet_socket:
            self.artnet_socket.close()
            self.artnet_socket = None

        self.connection_status_label.config(text="🔴 未连接", fg="#ef4444")
        self.status_label.config(text="Art-Net连接已断开")

        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")

        # 停止播放
        if self.is_playing:
            self.stop_playback()

    def _create_artnet_packet(self, universe, dmx_data):
        """创建Art-Net数据包"""
        packet = bytearray()
        packet.extend(b"Art-Net\x00")  # Art-Net ID
        packet.extend(struct.pack("<H", 0x5000))  # OpDmx
        packet.extend(struct.pack(">H", 14))  # Protocol version
        packet.append(0)  # Sequence
        packet.append(0)  # Physical

        # Art-Net Universe编号：Depence软件通常期望直接使用用户设置的Universe
        # 不进行转换，直接使用用户设置的Universe值
        artnet_universe = universe
        packet.append(artnet_universe & 0xFF)  # Universe low
        packet.append((artnet_universe >> 8) & 0xFF)  # Universe high
        packet.extend(struct.pack(">H", 512))  # Data length

        # 确保DMX数据长度为512
        dmx_data_copy = list(dmx_data)  # 创建副本避免修改原数据
        if len(dmx_data_copy) < 512:
            dmx_data_copy.extend([0] * (512 - len(dmx_data_copy)))
        elif len(dmx_data_copy) > 512:
            dmx_data_copy = dmx_data_copy[:512]

        packet.extend(dmx_data_copy)

        print(f"📦 创建Art-Net包: 用户Universe {universe} -> Art-Net Universe {artnet_universe}")
        return packet

    def start_playback(self):
        """开始播放"""
        if not self.lighting_sequence:
            messagebox.showwarning("警告", "请先生成AI灯光序列")
            return

        if not self.artnet_socket:
            messagebox.showwarning("警告", "请先连接Art-Net到MA控台")
            return

        if not self.is_playing:
            self.is_playing = True
            self.sequence_index = 0
            self.start_time = time.time()

            # 同时播放音乐
            if self.music_loaded and self.current_music_file:
                try:
                    pygame.mixer.music.load(self.current_music_file)
                    pygame.mixer.music.play()
                    print(f"🎵 开始播放音乐: {self.current_music_file}")
                except Exception as e:
                    print(f"音乐播放错误: {e}")

            self.play_btn.config(text="⏸️ 暂停")
            self.playback_thread = threading.Thread(target=self._playback_thread, daemon=True)
            self.playback_thread.start()

            self.status_label.config(text="开始播放AI灯光序列+音乐到MA控台...")

    def pause_playback(self):
        """暂停播放"""
        if self.is_playing:
            self.is_playing = False
            # 暂停音乐
            pygame.mixer.music.pause()
            self.play_btn.config(text="▶️ 播放")
            self.status_label.config(text="播放已暂停")
        else:
            # 恢复播放
            self.is_playing = True
            self.start_time = time.time() - (self.sequence_index * 0.1)
            # 恢复音乐
            pygame.mixer.music.unpause()
            self.play_btn.config(text="⏸️ 暂停")
            self.playback_thread = threading.Thread(target=self._playback_thread, daemon=True)
            self.playback_thread.start()
            self.status_label.config(text="恢复播放...")

    def stop_playback(self):
        """停止播放"""
        self.is_playing = False
        self.sequence_index = 0

        # 停止音乐
        pygame.mixer.music.stop()

        self.play_btn.config(text="▶️ 播放")
        self.playback_progress_var.set(0)
        self.time_label.config(text="00:00 / 00:00")
        self.status_label.config(text="播放已停止")

        # 清除所有灯具输出
        self._clear_all_outputs()

        # 清除活跃灯具显示
        for item in self.active_tree.get_children():
            self.active_tree.delete(item)

    def _playback_thread(self):
        """播放线程"""
        if not self.lighting_sequence or not self.music_analysis:
            return

        total_duration = self.music_analysis['duration']
        sorted_sequence = sorted(self.lighting_sequence, key=lambda x: x['time'])

        sent_packets = 0

        while self.is_playing and self.sequence_index < len(sorted_sequence):
            current_time = time.time() - self.start_time

            # 更新进度显示
            progress = (current_time / total_duration) * 100 if total_duration > 0 else 0
            self.root.after(0, lambda: self.playback_progress_var.set(min(progress, 100)))

            # 更新时间显示
            time_str = f"{int(current_time//60):02d}:{int(current_time%60):02d} / {int(total_duration//60):02d}:{int(total_duration%60):02d}"
            self.root.after(0, lambda: self.time_label.config(text=time_str))

            # 更新系统状态
            bpm = self.music_analysis.get('bpm', 0)
            self.root.after(0, lambda: self.current_time_label.config(text=f"播放时间: {current_time:.1f}s"))
            self.root.after(0, lambda: self.current_bpm_label.config(text=f"当前BPM: {bpm:.1f}"))
            self.root.after(0, lambda: self.sent_packets_label.config(text=f"发送包数: {sent_packets}"))

            # 处理当前时间的事件
            events_to_send = []
            while (self.sequence_index < len(sorted_sequence) and
                   sorted_sequence[self.sequence_index]['time'] <= current_time):

                events_to_send.append(sorted_sequence[self.sequence_index])
                self.sequence_index += 1

            # 发送Art-Net数据
            if events_to_send:
                universes_data = self._prepare_universe_data(events_to_send)

                for universe, dmx_data in universes_data.items():
                    try:
                        packet = self._create_artnet_packet(universe, dmx_data)
                        target_ip = self.artnet_ip_var.get()
                        target_port = int(self.artnet_port_var.get())
                        self.artnet_socket.sendto(packet, (target_ip, target_port))
                        sent_packets += 1
                    except Exception as e:
                        print(f"Art-Net发送错误: {e}")

                # 更新活跃灯具显示
                self.root.after(0, lambda: self._update_active_fixtures_display(events_to_send))

            # 检查是否播放完成
            if current_time >= total_duration:
                break

            time.sleep(0.02)  # 50Hz更新频率

        # 播放完成
        self.root.after(0, self.stop_playback)

    def _prepare_universe_data(self, events):
        """准备Universe数据"""
        universes_data = {}

        for event in events:
            universe = event['universe']
            start_address = event['start_address']
            dmx_values = event['dmx_values']

            # 初始化Universe数据
            if universe not in universes_data:
                universes_data[universe] = [0] * 512

            # 查找灯具定义
            fixture = None
            for f in self.configured_fixtures:
                if f['id'] == event['fixture_id']:
                    fixture = f
                    break

            if fixture:
                # 根据真实MA灯具库的通道顺序映射
                channel_mapping = self._get_real_channel_mapping(fixture)

                # 使用真实的MA通道映射
                for channel_name, value in dmx_values.items():
                    if channel_name in channel_mapping:
                        # 获取通道在灯具中的相对位置（1-based）
                        channel_offset = channel_mapping[channel_name]
                        # 正确的DMX地址计算：起始地址 + 通道偏移 - 1（因为数组从0开始，但通道偏移是1-based）
                        dmx_address = start_address + channel_offset - 1

                        if 0 <= dmx_address < 512:
                            final_value = max(0, min(255, int(value)))
                            universes_data[universe][dmx_address] = final_value
                            print(f"✅ 设置 Universe {universe}, DMX地址 {dmx_address+1}: {channel_name} (通道{channel_offset}) = {final_value}")
                        else:
                            print(f"❌ DMX地址超出范围: {dmx_address+1} (通道{channel_offset}: {channel_name})")
                    else:
                        print(f"⚠️ 未找到通道映射: {channel_name}")

        # 调试：显示Universe数据摘要
        for universe, data in universes_data.items():
            non_zero_channels = [(i+1, data[i]) for i in range(512) if data[i] > 0]
            if non_zero_channels:
                print(f"🌐 Universe {universe} 非零通道: {non_zero_channels[:10]}...")  # 只显示前10个

        return universes_data

    def _get_real_channel_mapping(self, fixture):
        """获取真实的MA通道映射"""
        # 根据ACME AECO 20的真实通道顺序（与XML文件完全匹配）
        if 'acme@aeco_20@37channel' in fixture['ma_type']:
            if fixture['mode'] == '37CH':
                return {
                    # 基于XML文件的真实通道名称
                    'Pan': 1,                    # 通道1: Pan
                    'Pan_fine': 2,               # 通道2: Pan Fine
                    'Tilt': 3,                   # 通道3: Tilt
                    'Tilt_fine': 4,              # 通道4: Tilt Fine
                    'Dimmer': 5,                 # 通道5: Dimmer
                    'Shutter_n_Strobe': 6,       # 通道6: Shutter/Strobe
                    'Red': 7,                    # 通道7: Red
                    'Green': 8,                  # 通道8: Green
                    'Blue': 9,                   # 通道9: Blue
                    'White': 10,                 # 通道10: White
                    'Amber': 11,                 # 通道11: Amber
                    'UV': 12,                    # 通道12: UV
                    'ColorWheel1': 13,           # 通道13: Color Wheel
                    'Gobo1': 14,                 # 通道14: Gobo1
                    'Gobo1Rot': 15,              # 通道15: Gobo1 Rotation
                    'Gobo2': 16,                 # 通道16: Gobo2
                    'Prism1': 17,                # 通道17: Prism
                    'Prism1Rot': 18,             # 通道18: Prism Rotation
                    'Focus1': 19,                # 通道19: Focus
                    'Zoom': 20,                  # 通道20: Zoom
                    'Iris1': 21,                 # 通道21: Iris
                    'Frost1': 22,                # 通道22: Frost
                    'Control1': 23,              # 通道23: Control
                    'Maintenance': 24,           # 通道24: Maintenance
                    # 兼容小写名称
                    'pan': 1, 'pan_fine': 2, 'tilt': 3, 'tilt_fine': 4,
                    'dimmer': 5, 'shutter_n_strobe': 6, 'red': 7, 'green': 8,
                    'blue': 9, 'white': 10, 'amber': 11, 'uv': 12,
                    'colorwheel1': 13, 'gobo1': 14, 'gobo1rot': 15, 'gobo2': 16,
                    'prism1': 17, 'prism1rot': 18, 'focus1': 19, 'zoom': 20,
                    'iris1': 21, 'frost1': 22, 'control1': 23, 'maintenance': 24
                }
            elif fixture['mode'] == '16CH':
                return {
                    'pan': 1,           # 通道1: Pan
                    'tilt': 2,          # 通道2: Tilt
                    'dimmer': 3,        # 通道3: Dimmer
                    'shutter_n_strobe': 4,  # 通道4: Shutter/Strobe
                    'red': 5,           # 通道5: Red
                    'green': 6,         # 通道6: Green
                    'blue': 7,          # 通道7: Blue
                    'white': 8,         # 通道8: White
                    'colorwheel1': 9,   # 通道9: Color Wheel
                    'gobo1': 10,        # 通道10: Gobo1
                    'gobo1rot': 11,     # 通道11: Gobo1 Rotation
                    'prism1': 12,       # 通道12: Prism
                    'focus1': 13,       # 通道13: Focus
                    'zoom': 14,         # 通道14: Zoom
                    'iris1': 15,        # 通道15: Iris
                    'control1': 16      # 通道16: Control
                }

        # 通用映射（如果不是ACME灯具）
        channel_names = list(fixture['channels'].keys())
        mapping = {}
        for i, name in enumerate(channel_names):
            mapping[name] = i + 1

        return mapping

    def _update_active_fixtures_display(self, events):
        """更新活跃灯具显示"""
        # 清除现有显示
        for item in self.active_tree.get_children():
            self.active_tree.delete(item)

        # 按灯具分组事件
        fixture_events = {}
        for event in events:
            fixture_id = event['fixture_id']
            if fixture_id not in fixture_events:
                fixture_events[fixture_id] = []
            fixture_events[fixture_id].append(event)

        # 显示每个活跃灯具
        active_count = 0
        for fixture_id, events_list in fixture_events.items():
            # 合并同一灯具的所有事件
            combined_event = events_list[0]  # 使用第一个事件作为基础

            # 计算平均强度
            all_values = []
            for event in events_list:
                all_values.extend(event['dmx_values'].values())

            avg_intensity = sum(all_values) / len(all_values) if all_values else 0

            # 颜色信息
            dmx_values = combined_event['dmx_values']
            if all(ch in dmx_values for ch in ['red', 'green', 'blue']):
                color_str = f"RGB({dmx_values['red']},{dmx_values['green']},{dmx_values['blue']})"
            elif 'dimmer' in dmx_values:
                color_str = f"调光({dmx_values['dimmer']})"
            else:
                color_str = "混合"

            # 状态
            status = "活跃" if avg_intensity > 10 else "待机"
            if status == "活跃":
                active_count += 1

            self.active_tree.insert("", "end", values=(
                combined_event['fixture_name'],
                combined_event['universe'],
                f"{combined_event['start_address']}-{combined_event['start_address'] + len(dmx_values) - 1}",
                f"{avg_intensity:.0f}",
                color_str,
                status
            ))

        # 更新活跃灯具计数
        self.active_fixtures_label.config(text=f"活跃灯具: {active_count}")

    def _clear_all_outputs(self):
        """清除所有输出"""
        if not self.artnet_socket:
            return

        try:
            # 获取所有使用的Universe
            universes = set(f['universe'] for f in self.configured_fixtures)

            # 发送全零数据包
            for universe in universes:
                packet = self._create_artnet_packet(universe, [0] * 512)
                target_ip = self.artnet_ip_var.get()
                target_port = int(self.artnet_port_var.get())
                self.artnet_socket.sendto(packet, (target_ip, target_port))

        except Exception as e:
            print(f"清除输出错误: {e}")

    def run(self):
        """运行应用"""
        # 绑定事件
        self.fixture_combo.bind('<<ComboboxSelected>>', self.on_fixture_selection_change)

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动主循环
        self.root.mainloop()

    def on_closing(self):
        """关闭应用"""
        # 停止播放
        if self.is_playing:
            self.stop_playback()

        # 断开Art-Net连接
        if self.artnet_socket:
            self.disconnect_artnet()

        self.root.destroy()

if __name__ == "__main__":
    print("🎭 专业MA AI灯光系统启动中...")

    try:
        app = ProfessionalMASystem()
        print("✅ 系统初始化完成")
        print("\n💡 专业使用流程:")
        print("   1. 📚 导入真实MA XML灯具库")
        print("   2. 🎵 导入音乐文件并AI分析")
        print("   3. 🔌 专业灯具配接到Universe")
        print("   4. 🤖 生成AI灯光序列")
        print("   5. 🔗 连接Art-Net到MA控台")
        print("   6. ▶️ 实时播放到MA控台")
        print("\n🎯 支持真实MA2/MA3控台XML灯具库格式")
        print("🌐 Art-Net直接输出到MA控台 (默认IP: *********)")
        print("🤖 AI智能分析音乐特征并生成专业灯光效果")

        app.run()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        import traceback
        traceback.print_exc()
