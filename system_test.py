#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统综合测试脚本
测试所有MA2控台通信功能
"""

import socket
import struct
import time
import threading
import json
from datetime import datetime

class MA2SystemTester:
    def __init__(self):
        self.test_results = {}
        self.ma2_ip = "*********"  # 从日志中看到的MA2 IP
        self.local_ip = "127.0.0.1"
        
    def log_test(self, test_name, status, details=""):
        """记录测试结果"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        result = {
            "timestamp": timestamp,
            "status": status,
            "details": details
        }
        self.test_results[test_name] = result
        
        status_icon = "✅" if status == "PASS" else "❌" if status == "FAIL" else "⚠️"
        print(f"{status_icon} [{timestamp}] {test_name}: {status}")
        if details:
            print(f"   详情: {details}")
    
    def test_art_net_listening(self):
        """测试Art-Net监听功能"""
        print("\n🔍 测试 1: Art-Net监听功能")
        try:
            # 创建UDP套接字监听Art-Net
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('', 6454))
            sock.settimeout(5.0)  # 5秒超时
            
            print("   正在监听Art-Net数据包...")
            start_time = time.time()
            packet_count = 0
            
            while time.time() - start_time < 5:
                try:
                    data, addr = sock.recvfrom(1024)
                    if len(data) >= 18 and data[:8] == b"Art-Net\x00":
                        packet_count += 1
                        if packet_count == 1:
                            print(f"   首个数据包来自: {addr[0]}")
                except socket.timeout:
                    break
            
            sock.close()
            
            if packet_count > 0:
                self.log_test("Art-Net监听", "PASS", f"接收到 {packet_count} 个数据包")
            else:
                self.log_test("Art-Net监听", "FAIL", "未接收到Art-Net数据包")
                
        except Exception as e:
            self.log_test("Art-Net监听", "FAIL", str(e))
    
    def test_art_net_sending(self):
        """测试Art-Net发送功能"""
        print("\n📤 测试 2: Art-Net发送功能")
        try:
            # 创建Art-Net DMX数据包
            packet = bytearray()
            packet.extend(b"Art-Net\x00")  # Art-Net ID
            packet.extend(struct.pack("<H", 0x5000))  # OpDmx
            packet.extend(struct.pack(">H", 14))  # Protocol version
            packet.append(0)  # Sequence
            packet.append(0)  # Physical
            packet.append(0)  # Universe low
            packet.append(0)  # Universe high
            packet.extend(struct.pack(">H", 512))  # Data length
            
            # 创建测试DMX数据
            dmx_data = [0] * 512
            dmx_data[0] = 255  # 通道1满值
            dmx_data[1] = 128  # 通道2半值
            dmx_data[2] = 64   # 通道3四分之一值
            packet.extend(dmx_data)
            
            # 发送到MA2
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            
            # 发送到MA2和广播地址
            targets = [
                (self.ma2_ip, 6454),
                ("***************", 6454)
            ]
            
            sent_count = 0
            for target_ip, port in targets:
                try:
                    sock.sendto(packet, (target_ip, port))
                    sent_count += 1
                    print(f"   发送到 {target_ip}:{port} 成功")
                except Exception as e:
                    print(f"   发送到 {target_ip}:{port} 失败: {e}")
            
            sock.close()
            
            if sent_count > 0:
                self.log_test("Art-Net发送", "PASS", f"成功发送到 {sent_count} 个目标")
            else:
                self.log_test("Art-Net发送", "FAIL", "所有发送目标都失败")
                
        except Exception as e:
            self.log_test("Art-Net发送", "FAIL", str(e))
    
    def test_midi_timecode(self):
        """测试MIDI时间码功能"""
        print("\n🎵 测试 3: MIDI时间码功能")
        try:
            # 测试pygame MIDI
            try:
                import pygame.midi
                if not pygame.midi.get_init():
                    pygame.midi.init()
                
                device_count = pygame.midi.get_count()
                print(f"   检测到 {device_count} 个MIDI设备")
                
                # 尝试发送MTC
                hours, minutes, seconds, frames = 0, 0, 10, 0
                mtc_data = [0xF0, 0x7F, 0x7F, 0x01, 0x01, hours, minutes, seconds, frames, 0xF7]
                
                # 查找输出设备
                output_found = False
                for i in range(device_count):
                    info = pygame.midi.get_device_info(i)
                    if info[3]:  # 输出设备
                        try:
                            midi_out = pygame.midi.Output(i)
                            for byte in mtc_data:
                                midi_out.write_short(byte)
                            midi_out.close()
                            output_found = True
                            print(f"   通过设备 {i} 发送MTC成功")
                            break
                        except:
                            continue
                
                pygame.midi.quit()
                
                if output_found:
                    self.log_test("MIDI时间码", "PASS", "成功发送MTC数据")
                else:
                    self.log_test("MIDI时间码", "WARNING", "无可用MIDI输出设备")
                    
            except ImportError:
                self.log_test("MIDI时间码", "WARNING", "pygame.midi不可用")
                
        except Exception as e:
            self.log_test("MIDI时间码", "FAIL", str(e))
    
    def test_mido_virtual_midi(self):
        """测试mido虚拟MIDI功能"""
        print("\n🎹 测试 4: 虚拟MIDI功能")
        try:
            import mido
            
            # 列出现有端口
            input_ports = mido.get_input_names()
            output_ports = mido.get_output_names()
            
            print(f"   现有输入端口: {input_ports}")
            print(f"   现有输出端口: {output_ports}")
            
            # 尝试创建虚拟端口
            try:
                virtual_port = mido.open_output('MA2_Test_Port', virtual=True)
                
                # 发送测试MTC消息
                mtc_msg = mido.Message('sysex', data=[0x7F, 0x7F, 0x01, 0x01, 0, 0, 15, 0])
                virtual_port.send(mtc_msg)
                
                virtual_port.close()
                self.log_test("虚拟MIDI", "PASS", "成功创建虚拟端口并发送消息")
                
            except Exception as ve:
                self.log_test("虚拟MIDI", "WARNING", f"虚拟端口创建失败: {ve}")
                
        except ImportError:
            self.log_test("虚拟MIDI", "WARNING", "mido库不可用")
        except Exception as e:
            self.log_test("虚拟MIDI", "FAIL", str(e))
    
    def test_network_timecode(self):
        """测试网络时间码发送"""
        print("\n🌐 测试 5: 网络时间码功能")
        try:
            # 测试SMPTE时间码发送
            timecode_data = "SMPTE:00:00:20:00"
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # 发送到多个端口
            targets = [
                (self.ma2_ip, 8000),      # MA2 SMPTE端口
                (self.local_ip, 8000),    # 本地SMPTE端口
                (self.local_ip, 9000),    # OSC端口
            ]
            
            sent_count = 0
            for target_ip, port in targets:
                try:
                    sock.sendto(timecode_data.encode(), (target_ip, port))
                    sent_count += 1
                    print(f"   时间码发送到 {target_ip}:{port} 成功")
                except Exception as e:
                    print(f"   时间码发送到 {target_ip}:{port} 失败: {e}")
            
            sock.close()
            
            if sent_count > 0:
                self.log_test("网络时间码", "PASS", f"成功发送到 {sent_count} 个目标")
            else:
                self.log_test("网络时间码", "FAIL", "所有发送目标都失败")
                
        except Exception as e:
            self.log_test("网络时间码", "FAIL", str(e))
    
    def test_msc_commands(self):
        """测试MSC命令发送"""
        print("\n🎛️ 测试 6: MSC命令功能")
        try:
            # 创建MSC GO命令
            msc_data = [0xF0, 0x7F, 0x00, 0x02, 0x7F, 0x01, 0x01, 0xF7]  # GO Cue 1
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            
            # 发送MSC命令
            targets = [
                (self.ma2_ip, 6000),      # MA2 MSC端口
                (self.local_ip, 6000),    # 本地MSC端口
            ]
            
            sent_count = 0
            for target_ip, port in targets:
                try:
                    sock.sendto(bytes(msc_data), (target_ip, port))
                    sent_count += 1
                    print(f"   MSC命令发送到 {target_ip}:{port} 成功")
                except Exception as e:
                    print(f"   MSC命令发送到 {target_ip}:{port} 失败: {e}")
            
            sock.close()
            
            if sent_count > 0:
                self.log_test("MSC命令", "PASS", f"成功发送到 {sent_count} 个目标")
            else:
                self.log_test("MSC命令", "FAIL", "所有发送目标都失败")
                
        except Exception as e:
            self.log_test("MSC命令", "FAIL", str(e))
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始MA2系统综合测试")
        print("=" * 50)
        
        # 运行所有测试
        self.test_art_net_listening()
        self.test_art_net_sending()
        self.test_midi_timecode()
        self.test_mido_virtual_midi()
        self.test_network_timecode()
        self.test_msc_commands()
        
        # 生成测试报告
        self.generate_report()
    
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 50)
        print("📊 测试报告")
        print("=" * 50)
        
        pass_count = 0
        fail_count = 0
        warning_count = 0
        
        for test_name, result in self.test_results.items():
            status = result["status"]
            if status == "PASS":
                pass_count += 1
                icon = "✅"
            elif status == "FAIL":
                fail_count += 1
                icon = "❌"
            else:
                warning_count += 1
                icon = "⚠️"
            
            print(f"{icon} {test_name}: {status}")
            if result["details"]:
                print(f"   {result['details']}")
        
        print("\n📈 统计:")
        print(f"   通过: {pass_count}")
        print(f"   失败: {fail_count}")
        print(f"   警告: {warning_count}")
        print(f"   总计: {len(self.test_results)}")
        
        # 保存报告到文件
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "pass": pass_count,
                "fail": fail_count,
                "warning": warning_count,
                "total": len(self.test_results)
            },
            "tests": self.test_results
        }
        
        with open("test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, indent=4, ensure_ascii=False)
        
        print(f"\n💾 详细报告已保存到: test_report.json")
        
        # 总体评估
        if fail_count == 0:
            if warning_count == 0:
                print("\n🎉 所有测试通过！系统运行完美！")
            else:
                print(f"\n✅ 核心功能正常，有 {warning_count} 个警告需要注意")
        else:
            print(f"\n⚠️ 发现 {fail_count} 个严重问题需要修复")

if __name__ == "__main__":
    tester = MA2SystemTester()
    tester.run_all_tests()
