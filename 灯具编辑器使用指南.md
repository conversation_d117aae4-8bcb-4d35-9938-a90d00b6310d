# 💡 灯具编辑器使用指南

## 📋 功能概述

灯具编辑器是多媒体演出控制中心的核心功能之一，提供完整的专业灯具管理解决方案：

- **灯具库管理** - 完整的灯具数据库系统
- **自定义灯具创建** - 支持创建任意类型的灯具
- **DMX模式配置** - 多模式DMX通道配置
- **协议支持设置** - 配置灯具支持的控制协议
- **导入导出功能** - 灯具数据的备份和共享
- **搜索筛选功能** - 快速查找所需灯具

## 🚀 快速开始

### 1. 打开灯具库

1. 启动多媒体演出控制中心
2. 登录系统
3. 进入 **"高级功能"** 标签页
4. 在灯具库管理区域点击 **"📚 灯具库"** 按钮

### 2. 浏览现有灯具

灯具库界面显示所有可用灯具：
- **灯具名称**: 显示灯具的完整名称
- **制造商**: 灯具制造商信息
- **分类**: 灯具类型分类
- **通道数**: DMX通道数量
- **描述**: 灯具功能描述

### 3. 搜索和筛选

#### 分类筛选
- 使用分类下拉菜单选择特定类型
- 支持的分类包括：
  - Generic (通用)
  - LED Par (LED帕灯)
  - Moving Head (摇头灯)
  - Wash Light (染色灯)
  - Beam Light (光束灯)
  - Strobe (频闪灯)
  - Laser (激光灯)
  - Haze Machine (烟雾机)

#### 文本搜索
- 在搜索框中输入关键词
- 支持按灯具名称或制造商搜索
- 实时搜索，输入即时显示结果

## ➕ 添加新灯具

### 1. 创建新灯具

1. 点击 **"➕ 添加灯具"** 按钮
2. 灯具编辑器对话框将打开

### 2. 填写基本信息

#### 必填字段
- **灯具名称**: 输入灯具的完整名称
- **制造商**: 输入制造商名称
- **通道数**: 输入DMX通道数量（必须大于0）

#### 可选字段
- **分类**: 从下拉菜单选择灯具类型
- **描述**: 输入灯具功能描述

### 3. 配置DMX模式

#### 添加DMX模式
1. 在DMX模式区域点击 **"➕ 添加模式"**
2. 填写模式信息：
   - **模式名称**: 如 "16ch", "8ch", "Basic"
   - **通道数**: 该模式使用的DMX通道数
   - **描述**: 模式功能说明
3. 点击 **"💾 保存"** 确认

#### 编辑DMX模式
1. 点击模式右侧的 **"✏️"** 编辑按钮
2. 修改模式信息
3. 保存更改

#### 删除DMX模式
1. 点击模式右侧的 **"🗑️"** 删除按钮
2. 确认删除操作

### 4. 设置协议支持

选择灯具支持的控制协议：
- **Art-Net**: 以太网灯光控制协议
- **sACN**: 流媒体ACN协议
- **OSC**: 开放声音控制协议
- **MSC**: MIDI演出控制协议

### 5. 测试协议功能

点击 **"🧪 测试协议"** 按钮：
- 验证选择的协议配置
- 模拟协议测试（实际协议功能需在主程序配置）
- 确保协议选择正确

### 6. 保存灯具

1. 检查所有信息是否正确
2. 点击 **"💾 保存"** 按钮
3. 灯具将添加到灯具库中

## ✏️ 编辑现有灯具

### 1. 选择要编辑的灯具

1. 在灯具库中找到目标灯具
2. 点击灯具行右侧的 **"✏️"** 编辑按钮

### 2. 修改灯具信息

- 所有字段都可以修改
- DMX模式可以添加、编辑或删除
- 协议支持可以重新配置

### 3. 保存更改

1. 完成修改后点击 **"💾 保存"**
2. 更改将立即生效

## 🗑️ 删除灯具

### 删除单个灯具
1. 在灯具库中找到要删除的灯具
2. 点击灯具行右侧的 **"🗑️"** 删除按钮
3. 确认删除操作

### 批量删除
1. 点击工具栏的 **"🗑️ 删除灯具"** 按钮
2. 按照提示选择要删除的灯具

## 📁 导入导出功能

### 导出灯具库

1. 点击 **"📤 导出灯具"** 按钮
2. 选择保存位置和文件名
3. 灯具库将保存为JSON格式文件

### 导入灯具库

1. 点击 **"📥 导入灯具"** 按钮
2. 选择要导入的JSON文件
3. 灯具将合并到现有灯具库中

### 导入GDTF文件

1. 点击 **"📁 导入GDTF"** 按钮
2. 选择GDTF格式的灯具文件
3. 系统将解析GDTF文件并创建灯具（功能开发中）

## 🔧 高级功能

### 灯具模板

#### 常用灯具模板
系统预置了常用灯具模板：
- Generic Dimmer (通用调光器)
- LED Par 64 (LED帕灯)
- Moving Head Spot (摇头聚光灯)
- Moving Head Wash (摇头染色灯)

#### 自定义模板
- 将常用配置保存为模板
- 快速创建相似类型的灯具
- 提高工作效率

### 批量操作

#### 批量编辑
- 选择多个灯具进行批量修改
- 统一设置制造商或分类
- 批量更新协议支持

#### 批量导入
- 从Excel或CSV文件批量导入灯具
- 支持标准格式的灯具数据
- 自动验证数据完整性

### 数据验证

#### 自动验证
- 检查必填字段完整性
- 验证通道数的有效性
- 确保DMX模式配置正确

#### 错误提示
- 详细的错误信息提示
- 指导用户正确填写
- 防止无效数据保存

## 📊 灯具统计

### 库存统计
- 总灯具数量
- 按分类统计
- 按制造商统计
- 按协议支持统计

### 使用分析
- 最常用的灯具类型
- 协议使用频率
- DMX模式分布

## 🔍 故障排除

### 常见问题

#### 保存失败
1. 检查必填字段是否完整
2. 验证通道数是否为正整数
3. 确保灯具名称不为空

#### 导入失败
1. 检查文件格式是否为JSON
2. 验证文件内容格式正确
3. 确保文件编码为UTF-8

#### 搜索无结果
1. 检查搜索关键词拼写
2. 尝试使用部分关键词
3. 清空搜索条件重新筛选

### 数据备份

#### 定期备份
- 建议定期导出灯具库
- 保存多个版本的备份文件
- 存储在安全的位置

#### 恢复数据
- 使用导入功能恢复备份
- 可以选择性导入部分灯具
- 支持合并多个备份文件

## 💡 使用技巧

### 命名规范
- 使用清晰的灯具名称
- 包含型号和主要特征
- 保持命名一致性

### 分类管理
- 合理使用分类功能
- 创建自定义分类
- 保持分类层次清晰

### DMX模式设计
- 为不同应用场景创建多个模式
- 使用描述性的模式名称
- 记录每个模式的用途

### 协议配置
- 根据实际设备选择协议
- 测试协议兼容性
- 记录协议特殊要求

---

## 📞 技术支持

如有问题或需要技术支持，请联系：
- **开发者**: 徐小龙
- **电话**: 15692899229
- **邮箱**: <EMAIL>

**版本**: V16 终极版
**更新日期**: 2025-06-18
