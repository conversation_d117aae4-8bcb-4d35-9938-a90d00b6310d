#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI辅助系统 - 音乐节拍检测、灯光效果推荐、场景自动生成
"""

import numpy as np
import librosa
import json
import random
import threading
import time
from datetime import datetime
from typing import List, Dict, Tuple, Optional
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.patches as patches

class MusicBeatDetector:
    """音乐节拍自动检测器"""
    
    def __init__(self):
        self.audio_data = None
        self.sample_rate = None
        self.beats = []
        self.tempo = 0
        self.beat_frames = []
        
    def load_audio(self, file_path: str) -> bool:
        """加载音频文件"""
        try:
            print(f"🎵 正在加载音频文件: {file_path}")
            self.audio_data, self.sample_rate = librosa.load(file_path, sr=22050)
            print(f"✅ 音频加载成功: 采样率={self.sample_rate}Hz, 时长={len(self.audio_data)/self.sample_rate:.2f}秒")
            return True
        except Exception as e:
            print(f"❌ 音频加载失败: {e}")
            return False
    
    def detect_beats(self) -> Dict:
        """检测音乐节拍"""
        if self.audio_data is None:
            return {"error": "未加载音频文件"}
        
        try:
            print("🔍 开始分析音乐节拍...")
            
            # 检测节拍
            tempo, beat_frames = librosa.beat.beat_track(
                y=self.audio_data, 
                sr=self.sample_rate,
                hop_length=512
            )
            
            # 转换为时间戳
            beat_times = librosa.frames_to_time(beat_frames, sr=self.sample_rate)
            
            # 分析音乐特征
            spectral_centroids = librosa.feature.spectral_centroid(y=self.audio_data, sr=self.sample_rate)[0]
            spectral_rolloff = librosa.feature.spectral_rolloff(y=self.audio_data, sr=self.sample_rate)[0]
            mfccs = librosa.feature.mfcc(y=self.audio_data, sr=self.sample_rate, n_mfcc=13)
            
            # 检测音乐情绪和风格
            energy = np.mean(librosa.feature.rms(y=self.audio_data)[0])
            brightness = np.mean(spectral_centroids)
            
            # 分类音乐风格
            music_style = self._classify_music_style(tempo, energy, brightness)
            mood = self._analyze_mood(energy, brightness, np.mean(mfccs[1:3], axis=0))
            
            self.tempo = tempo
            self.beats = beat_times
            self.beat_frames = beat_frames
            
            result = {
                "tempo": float(tempo),
                "beat_count": len(beat_times),
                "beat_times": beat_times.tolist(),
                "duration": float(len(self.audio_data) / self.sample_rate),
                "music_style": music_style,
                "mood": mood,
                "energy_level": float(energy),
                "brightness": float(brightness),
                "analysis_time": datetime.now().isoformat()
            }
            
            print(f"✅ 节拍分析完成:")
            print(f"   BPM: {tempo:.1f}")
            print(f"   节拍数: {len(beat_times)}")
            print(f"   音乐风格: {music_style}")
            print(f"   情绪: {mood}")
            
            return result
            
        except Exception as e:
            print(f"❌ 节拍检测失败: {e}")
            return {"error": str(e)}
    
    def _classify_music_style(self, tempo: float, energy: float, brightness: float) -> str:
        """分类音乐风格"""
        if tempo < 70:
            return "慢板/抒情" if energy < 0.1 else "慢摇/蓝调"
        elif tempo < 100:
            return "中板/流行" if brightness > 2000 else "民谣/古典"
        elif tempo < 130:
            return "快板/摇滚" if energy > 0.15 else "爵士/放克"
        elif tempo < 160:
            return "舞曲/电子" if brightness > 3000 else "摇滚/金属"
        else:
            return "电子舞曲/鼓点" if energy > 0.2 else "快节奏流行"
    
    def _analyze_mood(self, energy: float, brightness: float, mfcc_features: np.ndarray) -> str:
        """分析音乐情绪"""
        # 基于音频特征的简单情绪分析
        if energy > 0.15 and brightness > 2500:
            return "激昂/兴奋"
        elif energy < 0.08 and brightness < 1500:
            return "平静/忧郁"
        elif energy > 0.12:
            return "活跃/欢快"
        elif brightness < 2000:
            return "温和/舒缓"
        else:
            return "中性/平衡"

class LightingEffectRecommender:
    """灯光效果智能推荐系统"""
    
    def __init__(self):
        self.effect_templates = self._load_effect_templates()
        self.color_palettes = self._load_color_palettes()
        
    def _load_effect_templates(self) -> Dict:
        """加载灯光效果模板"""
        return {
            "慢板/抒情": {
                "primary_effects": ["渐变", "呼吸", "柔光"],
                "colors": ["暖白", "琥珀", "淡蓝"],
                "intensity": "低-中",
                "speed": "慢",
                "pattern": "平滑过渡"
            },
            "快板/摇滚": {
                "primary_effects": ["频闪", "追光", "扫描"],
                "colors": ["红", "白", "蓝"],
                "intensity": "高",
                "speed": "快",
                "pattern": "强烈对比"
            },
            "舞曲/电子": {
                "primary_effects": ["彩虹", "频闪", "旋转"],
                "colors": ["彩色循环", "霓虹色"],
                "intensity": "高",
                "speed": "极快",
                "pattern": "节拍同步"
            },
            "民谣/古典": {
                "primary_effects": ["渐变", "聚光", "柔光"],
                "colors": ["暖白", "金色", "淡紫"],
                "intensity": "中",
                "speed": "中慢",
                "pattern": "优雅过渡"
            },
            "爵士/放克": {
                "primary_effects": ["摆动", "聚光", "色彩变换"],
                "colors": ["琥珀", "深蓝", "紫色"],
                "intensity": "中-高",
                "speed": "中",
                "pattern": "律动感"
            }
        }
    
    def _load_color_palettes(self) -> Dict:
        """加载色彩调色板"""
        return {
            "激昂/兴奋": {
                "primary": ["#FF0000", "#FF4500", "#FFD700"],  # 红、橙红、金
                "secondary": ["#FFFFFF", "#FF69B4"],  # 白、粉
                "accent": ["#00FFFF", "#FF1493"]  # 青、深粉
            },
            "平静/忧郁": {
                "primary": ["#4169E1", "#6495ED", "#87CEEB"],  # 蓝色系
                "secondary": ["#E6E6FA", "#D8BFD8"],  # 淡紫色系
                "accent": ["#2F4F4F", "#708090"]  # 深灰色系
            },
            "活跃/欢快": {
                "primary": ["#FFD700", "#FFA500", "#FF6347"],  # 暖色系
                "secondary": ["#32CD32", "#00FF7F"],  # 绿色系
                "accent": ["#FF69B4", "#FF1493"]  # 粉色系
            },
            "温和/舒缓": {
                "primary": ["#F5DEB3", "#DDA0DD", "#98FB98"],  # 柔和色系
                "secondary": ["#FFEFD5", "#F0E68C"],  # 米色系
                "accent": ["#B0C4DE", "#87CEFA"]  # 淡蓝色系
            }
        }
    
    def recommend_effects(self, music_analysis: Dict) -> Dict:
        """基于音乐分析推荐灯光效果"""
        try:
            music_style = music_analysis.get("music_style", "中性")
            mood = music_analysis.get("mood", "中性/平衡")
            tempo = music_analysis.get("tempo", 120)
            energy = music_analysis.get("energy_level", 0.1)
            
            # 获取基础效果模板
            base_template = self.effect_templates.get(music_style, self.effect_templates["民谣/古典"])
            
            # 获取色彩方案
            color_palette = self.color_palettes.get(mood, self.color_palettes["温和/舒缓"])
            
            # 生成具体的灯光序列
            lighting_sequence = self._generate_lighting_sequence(
                music_analysis, base_template, color_palette
            )
            
            recommendation = {
                "music_style": music_style,
                "mood": mood,
                "base_template": base_template,
                "color_palette": color_palette,
                "lighting_sequence": lighting_sequence,
                "sync_points": self._generate_sync_points(music_analysis),
                "recommendation_confidence": self._calculate_confidence(music_analysis),
                "generated_time": datetime.now().isoformat()
            }
            
            print(f"✅ 灯光效果推荐完成:")
            print(f"   风格: {music_style}")
            print(f"   情绪: {mood}")
            print(f"   推荐置信度: {recommendation['recommendation_confidence']:.2f}")
            
            return recommendation
            
        except Exception as e:
            print(f"❌ 灯光效果推荐失败: {e}")
            return {"error": str(e)}
    
    def _generate_lighting_sequence(self, music_analysis: Dict, template: Dict, palette: Dict) -> List[Dict]:
        """生成具体的灯光序列"""
        sequence = []
        beat_times = music_analysis.get("beat_times", [])
        tempo = music_analysis.get("tempo", 120)
        
        # 根据节拍生成灯光变化点
        for i, beat_time in enumerate(beat_times[:50]):  # 限制前50个节拍
            if i % 4 == 0:  # 每4拍一个主要变化
                effect = {
                    "time": float(beat_time),
                    "type": "primary_change",
                    "effect": random.choice(template["primary_effects"]),
                    "color": random.choice(palette["primary"]),
                    "intensity": self._map_intensity(template["intensity"]),
                    "duration": 60 / tempo * 4  # 4拍的时长
                }
            elif i % 2 == 0:  # 每2拍一个次要变化
                effect = {
                    "time": float(beat_time),
                    "type": "secondary_change",
                    "effect": "intensity_change",
                    "color": random.choice(palette["secondary"]),
                    "intensity": self._map_intensity(template["intensity"]) * 0.7,
                    "duration": 60 / tempo * 2
                }
            else:  # 其他节拍的微调
                effect = {
                    "time": float(beat_time),
                    "type": "accent",
                    "effect": "flash" if template["speed"] == "快" else "dim",
                    "color": random.choice(palette["accent"]),
                    "intensity": self._map_intensity(template["intensity"]) * 0.3,
                    "duration": 60 / tempo * 0.5
                }
            
            sequence.append(effect)
        
        return sequence
    
    def _generate_sync_points(self, music_analysis: Dict) -> List[Dict]:
        """生成同步点"""
        beat_times = music_analysis.get("beat_times", [])
        sync_points = []
        
        # 每16拍创建一个同步点
        for i in range(0, len(beat_times), 16):
            if i < len(beat_times):
                sync_points.append({
                    "time": float(beat_times[i]),
                    "type": "major_sync",
                    "description": f"第{i//16 + 1}个乐段开始"
                })
        
        return sync_points
    
    def _map_intensity(self, intensity_desc: str) -> float:
        """映射强度描述到数值"""
        mapping = {
            "低": 0.3,
            "低-中": 0.5,
            "中": 0.7,
            "中-高": 0.8,
            "高": 1.0
        }
        return mapping.get(intensity_desc, 0.7)
    
    def _calculate_confidence(self, music_analysis: Dict) -> float:
        """计算推荐置信度"""
        # 基于音频分析质量计算置信度
        factors = []
        
        if "tempo" in music_analysis and music_analysis["tempo"] > 0:
            factors.append(0.3)
        
        if "beat_count" in music_analysis and music_analysis["beat_count"] > 10:
            factors.append(0.3)
        
        if "energy_level" in music_analysis:
            factors.append(0.2)
        
        if "brightness" in music_analysis:
            factors.append(0.2)
        
        return sum(factors)

class SceneAutoGenerator:
    """场景自动生成器"""
    
    def __init__(self):
        self.scene_templates = self._load_scene_templates()
        
    def _load_scene_templates(self) -> Dict:
        """加载场景模板"""
        return {
            "音乐会": {
                "stages": ["开场", "主题展示", "高潮", "尾声"],
                "lighting_zones": ["舞台前区", "舞台后区", "观众席", "背景"],
                "default_effects": ["聚光", "背景渲染", "氛围灯"],
                "transition_types": ["渐变", "切换", "淡入淡出"]
            },
            "戏剧": {
                "stages": ["序幕", "第一幕", "第二幕", "第三幕", "尾声"],
                "lighting_zones": ["主演区", "配角区", "背景区", "道具区"],
                "default_effects": ["角色跟光", "场景渲染", "情绪灯光"],
                "transition_types": ["场景切换", "时间过渡", "情绪转换"]
            },
            "展览": {
                "stages": ["入场", "展示区1", "展示区2", "展示区3", "出场"],
                "lighting_zones": ["展品区", "通道", "休息区", "互动区"],
                "default_effects": ["展品照明", "引导灯光", "装饰灯"],
                "transition_types": ["区域切换", "引导过渡", "重点突出"]
            }
        }
    
    def generate_scene(self, music_analysis: Dict, lighting_recommendation: Dict, 
                      scene_type: str = "音乐会") -> Dict:
        """自动生成完整场景"""
        try:
            template = self.scene_templates.get(scene_type, self.scene_templates["音乐会"])
            
            # 基于音乐时长分配场景段落
            duration = music_analysis.get("duration", 300)  # 默认5分钟
            stage_durations = self._calculate_stage_durations(duration, len(template["stages"]))
            
            # 生成场景序列
            scenes = []
            current_time = 0
            
            for i, stage in enumerate(template["stages"]):
                stage_duration = stage_durations[i]
                
                scene = {
                    "stage_name": stage,
                    "start_time": current_time,
                    "duration": stage_duration,
                    "end_time": current_time + stage_duration,
                    "lighting_setup": self._generate_stage_lighting(
                        stage, template, lighting_recommendation, current_time, stage_duration
                    ),
                    "transitions": self._generate_transitions(
                        stage, template, i, len(template["stages"])
                    )
                }
                
                scenes.append(scene)
                current_time += stage_duration
            
            # 生成完整的场景配置
            full_scene = {
                "scene_type": scene_type,
                "total_duration": duration,
                "music_analysis": music_analysis,
                "lighting_recommendation": lighting_recommendation,
                "scenes": scenes,
                "global_settings": self._generate_global_settings(template),
                "generated_time": datetime.now().isoformat(),
                "metadata": {
                    "generator_version": "1.0",
                    "confidence": lighting_recommendation.get("recommendation_confidence", 0.8)
                }
            }
            
            print(f"✅ 场景自动生成完成:")
            print(f"   场景类型: {scene_type}")
            print(f"   总时长: {duration:.1f}秒")
            print(f"   场景段落: {len(scenes)}个")
            
            return full_scene
            
        except Exception as e:
            print(f"❌ 场景生成失败: {e}")
            return {"error": str(e)}
    
    def _calculate_stage_durations(self, total_duration: float, stage_count: int) -> List[float]:
        """计算各阶段时长"""
        # 经典的场景时长分配比例
        if stage_count == 4:  # 音乐会模式
            ratios = [0.1, 0.3, 0.5, 0.1]  # 开场、展示、高潮、尾声
        elif stage_count == 5:  # 戏剧模式
            ratios = [0.05, 0.25, 0.35, 0.25, 0.1]  # 序幕、三幕、尾声
        else:  # 其他模式，平均分配
            ratios = [1.0 / stage_count] * stage_count
        
        return [total_duration * ratio for ratio in ratios]
    
    def _generate_stage_lighting(self, stage: str, template: Dict, 
                               lighting_rec: Dict, start_time: float, duration: float) -> Dict:
        """为特定阶段生成灯光配置"""
        lighting_sequence = lighting_rec.get("lighting_sequence", [])
        
        # 筛选该阶段的灯光事件
        stage_lighting = [
            event for event in lighting_sequence 
            if start_time <= event.get("time", 0) < start_time + duration
        ]
        
        return {
            "zone_configs": {
                zone: {
                    "primary_color": lighting_rec.get("color_palette", {}).get("primary", ["#FFFFFF"])[0],
                    "intensity": 0.8 if "主" in zone or "前" in zone else 0.5,
                    "effects": template["default_effects"]
                }
                for zone in template["lighting_zones"]
            },
            "lighting_events": stage_lighting,
            "stage_mood": self._determine_stage_mood(stage),
            "special_effects": self._generate_special_effects(stage, duration)
        }
    
    def _generate_transitions(self, stage: str, template: Dict, 
                            stage_index: int, total_stages: int) -> Dict:
        """生成场景转换"""
        if stage_index == total_stages - 1:  # 最后一个阶段
            return {"type": "fade_out", "duration": 3.0}
        
        transition_types = template["transition_types"]
        return {
            "type": random.choice(transition_types),
            "duration": 2.0,
            "fade_in": 1.0,
            "fade_out": 1.0
        }
    
    def _generate_global_settings(self, template: Dict) -> Dict:
        """生成全局设置"""
        return {
            "master_intensity": 1.0,
            "color_temperature": 3200,  # 暖白光
            "ambient_level": 0.1,
            "emergency_lighting": True,
            "sync_tolerance": 0.1  # 同步容差100ms
        }
    
    def _determine_stage_mood(self, stage: str) -> str:
        """确定阶段情绪"""
        mood_mapping = {
            "开场": "期待",
            "序幕": "神秘",
            "主题展示": "展示",
            "第一幕": "建立",
            "第二幕": "发展",
            "第三幕": "冲突",
            "高潮": "激昂",
            "尾声": "回归",
            "出场": "满足"
        }
        return mood_mapping.get(stage, "中性")
    
    def _generate_special_effects(self, stage: str, duration: float) -> List[Dict]:
        """生成特殊效果"""
        effects = []
        
        if "高潮" in stage:
            effects.append({
                "type": "strobe",
                "start_time": duration * 0.7,
                "duration": 5.0,
                "intensity": 1.0
            })
        
        if "开场" in stage or "序幕" in stage:
            effects.append({
                "type": "fade_in",
                "start_time": 0,
                "duration": 3.0,
                "intensity": 0.8
            })
        
        return effects

class AIAssistantGUI:
    """AI辅助系统图形界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 AI智能演出助手")
        self.root.geometry("1200x800")

        # 初始化AI组件
        self.beat_detector = MusicBeatDetector()
        self.effect_recommender = LightingEffectRecommender()
        self.scene_generator = SceneAutoGenerator()

        # 数据存储
        self.current_music_analysis = None
        self.current_lighting_recommendation = None
        self.current_scene = None
        self.audio_file_path = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_frame = tk.Frame(self.root, bg="#2C3E50", height=60)
        title_frame.pack(fill="x")
        title_frame.pack_propagate(False)

        title_label = tk.Label(title_frame, text="🤖 AI智能演出助手",
                              font=("Arial", 18, "bold"), fg="white", bg="#2C3E50")
        title_label.pack(expand=True)

        # 创建主要选项卡
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=5)

        # 音乐分析选项卡
        self.create_music_analysis_tab()

        # 灯光推荐选项卡
        self.create_lighting_recommendation_tab()

        # 场景生成选项卡
        self.create_scene_generation_tab()

        # 实时预览选项卡
        self.create_preview_tab()

        # 状态栏
        self.status_frame = tk.Frame(self.root, relief="sunken", bd=1)
        self.status_frame.pack(side="bottom", fill="x")

        self.status_label = tk.Label(self.status_frame, text="就绪 - 请选择音频文件开始分析", anchor="w")
        self.status_label.pack(side="left", padx=5)

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, variable=self.progress_var, length=200)
        self.progress_bar.pack(side="right", padx=5, pady=2)

    def create_music_analysis_tab(self):
        """创建音乐分析选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🎵 音乐分析")

        # 文件选择区域
        file_frame = ttk.LabelFrame(frame, text="音频文件选择")
        file_frame.pack(fill="x", padx=10, pady=5)

        self.file_path_var = tk.StringVar()
        file_entry = tk.Entry(file_frame, textvariable=self.file_path_var, width=60)
        file_entry.pack(side="left", padx=5, pady=5, fill="x", expand=True)

        browse_btn = tk.Button(file_frame, text="📁 浏览", command=self.browse_audio_file)
        browse_btn.pack(side="right", padx=5, pady=5)

        analyze_btn = tk.Button(file_frame, text="🔍 开始分析", command=self.analyze_music,
                               bg="#3498DB", fg="white", font=("Arial", 10, "bold"))
        analyze_btn.pack(side="right", padx=5, pady=5)

        # 分析结果显示区域
        results_frame = ttk.LabelFrame(frame, text="分析结果")
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 左侧：基本信息
        info_frame = tk.Frame(results_frame)
        info_frame.pack(side="left", fill="y", padx=5, pady=5)

        tk.Label(info_frame, text="基本信息", font=("Arial", 12, "bold")).pack(anchor="w")

        self.tempo_label = tk.Label(info_frame, text="BPM: --", font=("Arial", 10))
        self.tempo_label.pack(anchor="w", pady=2)

        self.duration_label = tk.Label(info_frame, text="时长: --", font=("Arial", 10))
        self.duration_label.pack(anchor="w", pady=2)

        self.beat_count_label = tk.Label(info_frame, text="节拍数: --", font=("Arial", 10))
        self.beat_count_label.pack(anchor="w", pady=2)

        self.style_label = tk.Label(info_frame, text="风格: --", font=("Arial", 10))
        self.style_label.pack(anchor="w", pady=2)

        self.mood_label = tk.Label(info_frame, text="情绪: --", font=("Arial", 10))
        self.mood_label.pack(anchor="w", pady=2)

        # 右侧：波形和节拍可视化
        viz_frame = tk.Frame(results_frame)
        viz_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)

        tk.Label(viz_frame, text="音频可视化", font=("Arial", 12, "bold")).pack(anchor="w")

        # 创建matplotlib图表
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(8, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, viz_frame)
        self.canvas.get_tk_widget().pack(fill="both", expand=True)

        # 详细分析结果文本框
        detail_frame = ttk.LabelFrame(frame, text="详细分析数据")
        detail_frame.pack(fill="x", padx=10, pady=5)

        self.analysis_text = tk.Text(detail_frame, height=8, font=("Consolas", 9))
        analysis_scroll = ttk.Scrollbar(detail_frame, orient="vertical", command=self.analysis_text.yview)
        self.analysis_text.configure(yscrollcommand=analysis_scroll.set)

        self.analysis_text.pack(side="left", fill="both", expand=True)
        analysis_scroll.pack(side="right", fill="y")

    def create_lighting_recommendation_tab(self):
        """创建灯光推荐选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="💡 灯光推荐")

        # 推荐控制区域
        control_frame = ttk.LabelFrame(frame, text="推荐控制")
        control_frame.pack(fill="x", padx=10, pady=5)

        recommend_btn = tk.Button(control_frame, text="✨ 生成灯光推荐",
                                command=self.generate_lighting_recommendation,
                                bg="#E74C3C", fg="white", font=("Arial", 10, "bold"))
        recommend_btn.pack(side="left", padx=5, pady=5)

        export_btn = tk.Button(control_frame, text="📤 导出配置", command=self.export_lighting_config)
        export_btn.pack(side="left", padx=5, pady=5)

        # 推荐结果显示
        results_frame = ttk.LabelFrame(frame, text="推荐结果")
        results_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 左侧：推荐概览
        overview_frame = tk.Frame(results_frame)
        overview_frame.pack(side="left", fill="y", padx=5, pady=5)

        tk.Label(overview_frame, text="推荐概览", font=("Arial", 12, "bold")).pack(anchor="w")

        self.rec_style_label = tk.Label(overview_frame, text="适配风格: --", font=("Arial", 10))
        self.rec_style_label.pack(anchor="w", pady=2)

        self.rec_mood_label = tk.Label(overview_frame, text="情绪匹配: --", font=("Arial", 10))
        self.rec_mood_label.pack(anchor="w", pady=2)

        self.confidence_label = tk.Label(overview_frame, text="置信度: --", font=("Arial", 10))
        self.confidence_label.pack(anchor="w", pady=2)

        # 色彩方案预览
        tk.Label(overview_frame, text="推荐色彩:", font=("Arial", 10, "bold")).pack(anchor="w", pady=(10,2))
        self.color_frame = tk.Frame(overview_frame)
        self.color_frame.pack(anchor="w", pady=2)

        # 右侧：详细配置
        config_frame = tk.Frame(results_frame)
        config_frame.pack(side="right", fill="both", expand=True, padx=5, pady=5)

        tk.Label(config_frame, text="灯光序列配置", font=("Arial", 12, "bold")).pack(anchor="w")

        # 灯光序列表格
        columns = ("时间", "类型", "效果", "颜色", "强度")
        self.lighting_tree = ttk.Treeview(config_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.lighting_tree.heading(col, text=col)
            self.lighting_tree.column(col, width=100)

        lighting_scroll = ttk.Scrollbar(config_frame, orient="vertical", command=self.lighting_tree.yview)
        self.lighting_tree.configure(yscrollcommand=lighting_scroll.set)

        self.lighting_tree.pack(side="left", fill="both", expand=True)
        lighting_scroll.pack(side="right", fill="y")

    def create_scene_generation_tab(self):
        """创建场景生成选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="🎭 场景生成")

        # 场景类型选择
        type_frame = ttk.LabelFrame(frame, text="场景类型设置")
        type_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(type_frame, text="演出类型:").pack(side="left", padx=5, pady=5)

        self.scene_type_var = tk.StringVar(value="音乐会")
        scene_types = ["音乐会", "戏剧", "展览"]
        scene_combo = ttk.Combobox(type_frame, textvariable=self.scene_type_var,
                                  values=scene_types, state="readonly")
        scene_combo.pack(side="left", padx=5, pady=5)

        generate_scene_btn = tk.Button(type_frame, text="🎬 生成完整场景",
                                     command=self.generate_complete_scene,
                                     bg="#27AE60", fg="white", font=("Arial", 10, "bold"))
        generate_scene_btn.pack(side="left", padx=20, pady=5)

        save_scene_btn = tk.Button(type_frame, text="💾 保存场景", command=self.save_scene)
        save_scene_btn.pack(side="left", padx=5, pady=5)

        # 场景时间轴显示
        timeline_frame = ttk.LabelFrame(frame, text="场景时间轴")
        timeline_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建时间轴画布
        self.timeline_canvas = tk.Canvas(timeline_frame, height=200, bg="white")
        timeline_scroll = ttk.Scrollbar(timeline_frame, orient="horizontal", command=self.timeline_canvas.xview)
        self.timeline_canvas.configure(xscrollcommand=timeline_scroll.set)

        self.timeline_canvas.pack(fill="both", expand=True)
        timeline_scroll.pack(fill="x")

        # 场景详情显示
        details_frame = ttk.LabelFrame(frame, text="场景详情")
        details_frame.pack(fill="x", padx=10, pady=5)

        self.scene_details_text = tk.Text(details_frame, height=10, font=("Consolas", 9))
        details_scroll = ttk.Scrollbar(details_frame, orient="vertical", command=self.scene_details_text.yview)
        self.scene_details_text.configure(yscrollcommand=details_scroll.set)

        self.scene_details_text.pack(side="left", fill="both", expand=True)
        details_scroll.pack(side="right", fill="y")

    def create_preview_tab(self):
        """创建实时预览选项卡"""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="👁️ 实时预览")

        # 播放控制
        control_frame = ttk.LabelFrame(frame, text="播放控制")
        control_frame.pack(fill="x", padx=10, pady=5)

        self.play_btn = tk.Button(control_frame, text="▶️ 播放预览", command=self.start_preview)
        self.play_btn.pack(side="left", padx=5, pady=5)

        self.pause_btn = tk.Button(control_frame, text="⏸️ 暂停", command=self.pause_preview)
        self.pause_btn.pack(side="left", padx=5, pady=5)

        self.stop_btn = tk.Button(control_frame, text="⏹️ 停止", command=self.stop_preview)
        self.stop_btn.pack(side="left", padx=5, pady=5)

        # 时间显示
        self.time_label = tk.Label(control_frame, text="00:00 / 00:00", font=("Arial", 12))
        self.time_label.pack(side="right", padx=10, pady=5)

        # 进度条
        self.preview_progress = ttk.Scale(control_frame, from_=0, to=100, orient="horizontal", length=300)
        self.preview_progress.pack(side="right", padx=5, pady=5)

        # 预览显示区域
        preview_frame = ttk.LabelFrame(frame, text="灯光效果预览")
        preview_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建灯光预览画布
        self.preview_canvas = tk.Canvas(preview_frame, bg="black", height=400)
        self.preview_canvas.pack(fill="both", expand=True, padx=5, pady=5)

        # 当前效果信息
        info_frame = tk.Frame(frame)
        info_frame.pack(fill="x", padx=10, pady=5)

        self.current_effect_label = tk.Label(info_frame, text="当前效果: --",
                                           font=("Arial", 10), anchor="w")
        self.current_effect_label.pack(side="left", padx=5)

        self.current_color_label = tk.Label(info_frame, text="当前颜色: --",
                                          font=("Arial", 10), anchor="w")
        self.current_color_label.pack(side="right", padx=5)

    def browse_audio_file(self):
        """浏览选择音频文件"""
        file_types = [
            ("音频文件", "*.mp3 *.wav *.flac *.m4a *.aac"),
            ("MP3文件", "*.mp3"),
            ("WAV文件", "*.wav"),
            ("所有文件", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=file_types
        )

        if file_path:
            self.file_path_var.set(file_path)
            self.audio_file_path = file_path
            self.status_label.config(text=f"已选择文件: {file_path}")

    def analyze_music(self):
        """分析音乐"""
        if not self.audio_file_path:
            messagebox.showwarning("警告", "请先选择音频文件")
            return

        # 在新线程中执行分析，避免界面冻结
        analysis_thread = threading.Thread(target=self._analyze_music_thread, daemon=True)
        analysis_thread.start()

    def _analyze_music_thread(self):
        """音乐分析线程"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="正在加载音频文件..."))
            self.root.after(0, lambda: self.progress_var.set(10))

            # 加载音频
            if not self.beat_detector.load_audio(self.audio_file_path):
                self.root.after(0, lambda: messagebox.showerror("错误", "音频文件加载失败"))
                return

            self.root.after(0, lambda: self.status_label.config(text="正在分析音乐节拍..."))
            self.root.after(0, lambda: self.progress_var.set(50))

            # 执行分析
            analysis_result = self.beat_detector.detect_beats()

            if "error" in analysis_result:
                self.root.after(0, lambda: messagebox.showerror("错误", f"分析失败: {analysis_result['error']}"))
                return

            self.current_music_analysis = analysis_result

            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_label.config(text="音乐分析完成"))

            # 更新界面显示
            self.root.after(0, self.update_music_analysis_display)

        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"分析过程出错: {e}"))
        finally:
            self.root.after(0, lambda: self.progress_var.set(0))

    def update_music_analysis_display(self):
        """更新音乐分析显示"""
        if not self.current_music_analysis:
            return

        analysis = self.current_music_analysis

        # 更新基本信息标签
        self.tempo_label.config(text=f"BPM: {analysis.get('tempo', 0):.1f}")
        self.duration_label.config(text=f"时长: {analysis.get('duration', 0):.1f}秒")
        self.beat_count_label.config(text=f"节拍数: {analysis.get('beat_count', 0)}")
        self.style_label.config(text=f"风格: {analysis.get('music_style', '未知')}")
        self.mood_label.config(text=f"情绪: {analysis.get('mood', '未知')}")

        # 更新可视化图表
        self.update_audio_visualization()

        # 更新详细分析文本
        self.analysis_text.delete("1.0", "end")
        analysis_text = json.dumps(analysis, indent=2, ensure_ascii=False)
        self.analysis_text.insert("1.0", analysis_text)

    def update_audio_visualization(self):
        """更新音频可视化"""
        if not self.beat_detector.audio_data is not None:
            return

        try:
            # 清除之前的图表
            self.ax1.clear()
            self.ax2.clear()

            # 绘制音频波形
            time_axis = np.linspace(0, len(self.beat_detector.audio_data) / self.beat_detector.sample_rate,
                                  len(self.beat_detector.audio_data))

            # 降采样以提高绘制速度
            downsample_factor = max(1, len(self.beat_detector.audio_data) // 10000)
            time_downsampled = time_axis[::downsample_factor]
            audio_downsampled = self.beat_detector.audio_data[::downsample_factor]

            self.ax1.plot(time_downsampled, audio_downsampled, color='blue', alpha=0.7)
            self.ax1.set_title("音频波形")
            self.ax1.set_xlabel("时间 (秒)")
            self.ax1.set_ylabel("振幅")

            # 绘制节拍标记
            if hasattr(self.beat_detector, 'beats') and len(self.beat_detector.beats) > 0:
                for beat_time in self.beat_detector.beats[:50]:  # 只显示前50个节拍
                    self.ax1.axvline(x=beat_time, color='red', alpha=0.6, linestyle='--')

            # 绘制节拍间隔分布
            if hasattr(self.beat_detector, 'beats') and len(self.beat_detector.beats) > 1:
                beat_intervals = np.diff(self.beat_detector.beats)
                self.ax2.hist(beat_intervals, bins=20, color='green', alpha=0.7)
                self.ax2.set_title("节拍间隔分布")
                self.ax2.set_xlabel("间隔 (秒)")
                self.ax2.set_ylabel("频次")

            self.fig.tight_layout()
            self.canvas.draw()

        except Exception as e:
            print(f"可视化更新失败: {e}")

    def generate_lighting_recommendation(self):
        """生成灯光推荐"""
        if not self.current_music_analysis:
            messagebox.showwarning("警告", "请先完成音乐分析")
            return

        try:
            self.status_label.config(text="正在生成灯光推荐...")
            self.progress_var.set(30)

            # 生成推荐
            recommendation = self.effect_recommender.recommend_effects(self.current_music_analysis)

            if "error" in recommendation:
                messagebox.showerror("错误", f"推荐生成失败: {recommendation['error']}")
                return

            self.current_lighting_recommendation = recommendation
            self.progress_var.set(100)
            self.status_label.config(text="灯光推荐生成完成")

            # 更新显示
            self.update_lighting_recommendation_display()

        except Exception as e:
            messagebox.showerror("错误", f"推荐生成过程出错: {e}")
        finally:
            self.progress_var.set(0)

    def update_lighting_recommendation_display(self):
        """更新灯光推荐显示"""
        if not self.current_lighting_recommendation:
            return

        rec = self.current_lighting_recommendation

        # 更新概览信息
        self.rec_style_label.config(text=f"适配风格: {rec.get('music_style', '未知')}")
        self.rec_mood_label.config(text=f"情绪匹配: {rec.get('mood', '未知')}")
        self.confidence_label.config(text=f"置信度: {rec.get('recommendation_confidence', 0):.2f}")

        # 更新色彩预览
        self.update_color_preview(rec.get('color_palette', {}))

        # 更新灯光序列表格
        self.update_lighting_sequence_table(rec.get('lighting_sequence', []))

    def update_color_preview(self, color_palette: Dict):
        """更新色彩预览"""
        # 清除之前的色彩显示
        for widget in self.color_frame.winfo_children():
            widget.destroy()

        # 显示主要色彩
        primary_colors = color_palette.get('primary', [])
        for i, color in enumerate(primary_colors[:5]):  # 最多显示5个颜色
            color_label = tk.Label(self.color_frame, bg=color, width=3, height=1, relief="solid")
            color_label.pack(side="left", padx=2)

    def update_lighting_sequence_table(self, lighting_sequence: List[Dict]):
        """更新灯光序列表格"""
        # 清除现有数据
        for item in self.lighting_tree.get_children():
            self.lighting_tree.delete(item)

        # 添加新数据
        for event in lighting_sequence[:100]:  # 限制显示数量
            time_str = f"{event.get('time', 0):.2f}s"
            event_type = event.get('type', '未知')
            effect = event.get('effect', '未知')
            color = event.get('color', '#FFFFFF')
            intensity = f"{event.get('intensity', 0):.2f}"

            self.lighting_tree.insert("", "end", values=(time_str, event_type, effect, color, intensity))

    def export_lighting_config(self):
        """导出灯光配置"""
        if not self.current_lighting_recommendation:
            messagebox.showwarning("警告", "没有可导出的灯光配置")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存灯光配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_lighting_recommendation, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", f"灯光配置已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def generate_complete_scene(self):
        """生成完整场景"""
        if not self.current_music_analysis:
            messagebox.showwarning("警告", "请先完成音乐分析")
            return

        if not self.current_lighting_recommendation:
            messagebox.showwarning("警告", "请先生成灯光推荐")
            return

        try:
            self.status_label.config(text="正在生成完整场景...")
            self.progress_var.set(50)

            scene_type = self.scene_type_var.get()
            scene = self.scene_generator.generate_scene(
                self.current_music_analysis,
                self.current_lighting_recommendation,
                scene_type
            )

            if "error" in scene:
                messagebox.showerror("错误", f"场景生成失败: {scene['error']}")
                return

            self.current_scene = scene
            self.progress_var.set(100)
            self.status_label.config(text="完整场景生成完成")

            # 更新显示
            self.update_scene_display()

        except Exception as e:
            messagebox.showerror("错误", f"场景生成过程出错: {e}")
        finally:
            self.progress_var.set(0)

    def update_scene_display(self):
        """更新场景显示"""
        if not self.current_scene:
            return

        # 更新时间轴
        self.draw_timeline()

        # 更新场景详情
        self.update_scene_details()

    def draw_timeline(self):
        """绘制场景时间轴"""
        self.timeline_canvas.delete("all")

        if not self.current_scene:
            return

        scenes = self.current_scene.get('scenes', [])
        total_duration = self.current_scene.get('total_duration', 300)

        # 画布尺寸
        canvas_width = max(800, total_duration * 2)  # 每秒2像素
        canvas_height = 180
        self.timeline_canvas.config(scrollregion=(0, 0, canvas_width, canvas_height))

        # 绘制时间刻度
        for i in range(0, int(total_duration) + 1, 30):  # 每30秒一个刻度
            x = i * 2
            self.timeline_canvas.create_line(x, 0, x, 20, fill="gray")
            self.timeline_canvas.create_text(x, 30, text=f"{i}s", anchor="n")

        # 绘制场景段落
        colors = ["#3498DB", "#E74C3C", "#27AE60", "#F39C12", "#9B59B6"]
        y_start = 50
        bar_height = 40

        for i, scene in enumerate(scenes):
            start_time = scene.get('start_time', 0)
            duration = scene.get('duration', 0)
            stage_name = scene.get('stage_name', f'场景{i+1}')

            x1 = start_time * 2
            x2 = (start_time + duration) * 2
            color = colors[i % len(colors)]

            # 绘制场景条
            rect = self.timeline_canvas.create_rectangle(
                x1, y_start, x2, y_start + bar_height,
                fill=color, outline="black", width=2
            )

            # 添加场景名称
            text_x = (x1 + x2) / 2
            text_y = y_start + bar_height / 2
            self.timeline_canvas.create_text(
                text_x, text_y, text=stage_name,
                fill="white", font=("Arial", 10, "bold")
            )

            # 添加时长信息
            self.timeline_canvas.create_text(
                text_x, y_start + bar_height + 15,
                text=f"{duration:.1f}s",
                fill="black", font=("Arial", 8)
            )

        # 绘制灯光事件标记
        if self.current_lighting_recommendation:
            lighting_sequence = self.current_lighting_recommendation.get('lighting_sequence', [])
            for event in lighting_sequence[:20]:  # 只显示前20个事件
                event_time = event.get('time', 0)
                x = event_time * 2

                # 绘制事件标记
                self.timeline_canvas.create_line(x, y_start + bar_height + 30, x, y_start + bar_height + 50,
                                               fill="red", width=2)
                self.timeline_canvas.create_oval(x-3, y_start + bar_height + 47, x+3, y_start + bar_height + 53,
                                               fill="red", outline="darkred")

    def update_scene_details(self):
        """更新场景详情"""
        self.scene_details_text.delete("1.0", "end")

        if not self.current_scene:
            return

        # 格式化场景信息
        details = f"场景类型: {self.current_scene.get('scene_type', '未知')}\n"
        details += f"总时长: {self.current_scene.get('total_duration', 0):.1f}秒\n"
        details += f"生成时间: {self.current_scene.get('generated_time', '未知')}\n\n"

        details += "=== 场景段落详情 ===\n\n"

        scenes = self.current_scene.get('scenes', [])
        for i, scene in enumerate(scenes):
            details += f"【{i+1}】{scene.get('stage_name', '未命名场景')}\n"
            details += f"  时间: {scene.get('start_time', 0):.1f}s - {scene.get('end_time', 0):.1f}s\n"
            details += f"  时长: {scene.get('duration', 0):.1f}s\n"

            # 灯光配置
            lighting_setup = scene.get('lighting_setup', {})
            stage_mood = lighting_setup.get('stage_mood', '未知')
            details += f"  情绪: {stage_mood}\n"

            # 灯光区域配置
            zone_configs = lighting_setup.get('zone_configs', {})
            if zone_configs:
                details += f"  灯光区域:\n"
                for zone, config in zone_configs.items():
                    color = config.get('primary_color', '#FFFFFF')
                    intensity = config.get('intensity', 0)
                    details += f"    {zone}: {color} (强度: {intensity:.2f})\n"

            details += "\n"

        self.scene_details_text.insert("1.0", details)

    def save_scene(self):
        """保存场景"""
        if not self.current_scene:
            messagebox.showwarning("警告", "没有可保存的场景")
            return

        file_path = filedialog.asksaveasfilename(
            title="保存场景配置",
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.current_scene, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", f"场景配置已保存到: {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {e}")

    def start_preview(self):
        """开始预览"""
        if not self.current_scene:
            messagebox.showwarning("警告", "请先生成完整场景")
            return

        self.status_label.config(text="开始预览播放...")
        # 这里可以添加实际的预览播放逻辑
        messagebox.showinfo("预览", "预览功能正在开发中...")

    def pause_preview(self):
        """暂停预览"""
        self.status_label.config(text="预览已暂停")

    def stop_preview(self):
        """停止预览"""
        self.status_label.config(text="预览已停止")

    def run(self):
        """运行应用"""
        self.root.mainloop()

# 安装依赖检查和提示
def check_dependencies():
    """检查必要的依赖库"""
    missing_deps = []

    try:
        import librosa
    except ImportError:
        missing_deps.append("librosa")

    try:
        import matplotlib
    except ImportError:
        missing_deps.append("matplotlib")

    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")

    if missing_deps:
        print("❌ 缺少必要的依赖库:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_deps)}")
        return False

    return True

if __name__ == "__main__":
    print("🤖 AI智能演出助手启动中...")

    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，程序退出")
        exit(1)

    print("✅ 依赖检查通过")

    try:
        app = AIAssistantGUI()
        print("✅ AI助手界面初始化完成")
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()
