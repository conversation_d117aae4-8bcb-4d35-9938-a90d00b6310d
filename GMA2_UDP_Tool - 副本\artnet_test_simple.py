#!/usr/bin/env python3
"""
简化的Art-Net测试程序
用于验证Art-Net功能是否正常工作
"""

import tkinter as tk
from tkinter import messagebox
import customtkinter as ctk
import socket
import struct
import threading
import time

class SimpleArtNetTester(ctk.CTk):
    def __init__(self):
        super().__init__()
        
        self.title("🎭 Art-Net 简化测试工具")
        self.geometry("600x400")
        
        # 设置主题
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.create_interface()
        
    def create_interface(self):
        """创建界面"""
        # 标题
        title_label = ctk.CTkLabel(self, text="🎭 Art-Net 简化测试工具", 
                                  font=("", 20, "bold"))
        title_label.pack(pady=20)
        
        # 配置框架
        config_frame = ctk.CTkFrame(self)
        config_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(config_frame, text="Art-Net 配置", 
                    font=("", 16, "bold")).pack(pady=10)
        
        # 配置参数
        params_frame = ctk.CTkFrame(config_frame, fg_color="transparent")
        params_frame.pack(fill="x", padx=10, pady=10)
        
        # Universe
        ctk.CTkLabel(params_frame, text="Universe:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        self.universe_entry = ctk.CTkEntry(params_frame, width=100)
        self.universe_entry.grid(row=0, column=1, padx=5, pady=5)
        self.universe_entry.insert(0, "1")
        
        # Subnet
        ctk.CTkLabel(params_frame, text="Subnet:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        self.subnet_entry = ctk.CTkEntry(params_frame, width=100)
        self.subnet_entry.grid(row=0, column=3, padx=5, pady=5)
        self.subnet_entry.insert(0, "0")
        
        # Net
        ctk.CTkLabel(params_frame, text="Net:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        self.net_entry = ctk.CTkEntry(params_frame, width=100)
        self.net_entry.grid(row=1, column=1, padx=5, pady=5)
        self.net_entry.insert(0, "0")
        
        # IP地址
        ctk.CTkLabel(params_frame, text="目标IP:").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        self.ip_entry = ctk.CTkEntry(params_frame, width=150)
        self.ip_entry.grid(row=1, column=3, padx=5, pady=5)
        self.ip_entry.insert(0, "***************")
        
        # 测试按钮
        button_frame = ctk.CTkFrame(self)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkButton(button_frame, text="🧪 基本测试", width=120,
                     command=self.test_basic).pack(side="left", padx=5, pady=10)
        
        ctk.CTkButton(button_frame, text="💡 全亮测试", width=120,
                     command=self.test_full_on).pack(side="left", padx=5, pady=10)
        
        ctk.CTkButton(button_frame, text="🌑 全暗测试", width=120,
                     command=self.test_all_off).pack(side="left", padx=5, pady=10)
        
        ctk.CTkButton(button_frame, text="🔍 网络诊断", width=120,
                     command=self.network_diagnostics).pack(side="left", padx=5, pady=10)
        
        # 结果显示
        result_frame = ctk.CTkFrame(self)
        result_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(result_frame, text="测试结果", 
                    font=("", 14, "bold")).pack(pady=5)
        
        self.result_text = ctk.CTkTextbox(result_frame, height=200)
        self.result_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        self.log("🎭 Art-Net 简化测试工具已启动")
        self.log("请配置参数后点击测试按钮")
        
    def log(self, message):
        """记录日志"""
        self.result_text.insert("end", f"{message}\n")
        self.result_text.see("end")
        self.update()
        
    def get_config(self):
        """获取配置参数"""
        try:
            universe = int(self.universe_entry.get() or "1")
            subnet = int(self.subnet_entry.get() or "0")
            net = int(self.net_entry.get() or "0")
            target_ip = self.ip_entry.get() or "***************"
            return universe, subnet, net, target_ip
        except ValueError as e:
            self.log(f"❌ 配置参数错误: {e}")
            return None
            
    def create_artnet_packet(self, universe, subnet, net, dmx_data):
        """创建Art-Net数据包"""
        try:
            # 确保DMX数据长度为512
            if len(dmx_data) < 512:
                dmx_data = list(dmx_data) + [0] * (512 - len(dmx_data))
            elif len(dmx_data) > 512:
                dmx_data = dmx_data[:512]

            # Art-Net头部
            header = b"Art-Net\x00"
            opcode = struct.pack("<H", 0x5000)
            protocol_version = struct.pack(">H", 14)
            sequence = struct.pack("B", 0)
            physical = struct.pack("B", 0)

            # Universe地址计算
            universe_low = universe & 0xFF
            universe_high = ((net & 0x7F) << 1) | ((subnet & 0x0F) << 4) | ((universe >> 8) & 0x0F)
            universe_bytes = struct.pack("BB", universe_low, universe_high)

            # 数据长度
            data_length = len(dmx_data)
            if data_length % 2 != 0:
                data_length += 1
                dmx_data.append(0)
            
            length = struct.pack(">H", data_length)

            # 组装数据包
            packet = (header + opcode + protocol_version + sequence + 
                     physical + universe_bytes + length + bytes(dmx_data))

            return packet

        except Exception as e:
            self.log(f"❌ 创建Art-Net数据包失败: {e}")
            return None
            
    def send_artnet_packet(self, packet_data, target_ip):
        """发送Art-Net数据包"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            sock.sendto(packet_data, (target_ip, 6454))
            sock.close()
            
            self.log(f"✅ Art-Net数据包已发送到 {target_ip}:6454")
            self.log(f"   数据包大小: {len(packet_data)} 字节")
            return True
            
        except Exception as e:
            self.log(f"❌ Art-Net数据包发送失败: {e}")
            return False
            
    def test_basic(self):
        """基本测试"""
        config = self.get_config()
        if not config:
            return
            
        universe, subnet, net, target_ip = config
        self.log(f"🧪 开始基本测试: Universe={universe}, Subnet={subnet}, Net={net}")
        
        # 前10个通道全亮
        dmx_data = [255] * 10 + [0] * 502
        packet = self.create_artnet_packet(universe, subnet, net, dmx_data)
        
        if packet:
            success = self.send_artnet_packet(packet, target_ip)
            if success:
                self.log("✅ 基本测试完成")
            else:
                self.log("❌ 基本测试失败")
        else:
            self.log("❌ 数据包创建失败")
            
    def test_full_on(self):
        """全亮测试"""
        config = self.get_config()
        if not config:
            return
            
        universe, subnet, net, target_ip = config
        self.log(f"💡 开始全亮测试: Universe={universe}")
        
        # 所有通道全亮
        dmx_data = [255] * 512
        packet = self.create_artnet_packet(universe, subnet, net, dmx_data)
        
        if packet:
            success = self.send_artnet_packet(packet, target_ip)
            if success:
                self.log("✅ 全亮测试完成")
            else:
                self.log("❌ 全亮测试失败")
        else:
            self.log("❌ 数据包创建失败")
            
    def test_all_off(self):
        """全暗测试"""
        config = self.get_config()
        if not config:
            return
            
        universe, subnet, net, target_ip = config
        self.log(f"🌑 开始全暗测试: Universe={universe}")
        
        # 所有通道关闭
        dmx_data = [0] * 512
        packet = self.create_artnet_packet(universe, subnet, net, dmx_data)
        
        if packet:
            success = self.send_artnet_packet(packet, target_ip)
            if success:
                self.log("✅ 全暗测试完成")
            else:
                self.log("❌ 全暗测试失败")
        else:
            self.log("❌ 数据包创建失败")
            
    def network_diagnostics(self):
        """网络诊断"""
        self.log("🔍 开始网络诊断...")
        
        try:
            # 检查Art-Net端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.bind(('', 6454))
            sock.close()
            self.log("✅ Art-Net端口 6454 可用")
        except Exception as e:
            self.log(f"❌ Art-Net端口 6454 不可用: {e}")
        
        try:
            # 测试广播功能
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            test_data = b"TEST_BROADCAST"
            sock.sendto(test_data, ('***************', 6454))
            sock.close()
            self.log("✅ 广播功能正常")
        except Exception as e:
            self.log(f"❌ 广播功能异常: {e}")
            
        self.log("🔍 网络诊断完成")

if __name__ == "__main__":
    app = SimpleArtNetTester()
    app.mainloop()
