#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MA2控台通信测试工具 - 简化版
专注于核心功能验证
"""

import tkinter as tk
from tkinter import ttk, messagebox
import socket
import struct
import threading
import time
from datetime import datetime

class MA2TestTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎭 MA2控台通信测试工具")
        self.root.geometry("800x600")
        
        # 状态变量
        self.listening = False
        self.sending = False
        self.packet_count = 0
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主标题
        title_label = tk.Label(self.root, text="🎭 MA2控台通信测试工具", 
                              font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 创建选项卡
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Art-Net监听选项卡
        self.create_artnet_listener_tab(notebook)
        
        # Art-Net发送选项卡
        self.create_artnet_sender_tab(notebook)
        
        # 时间码测试选项卡
        self.create_timecode_tab(notebook)
        
        # 状态栏
        self.status_label = tk.Label(self.root, text="就绪", relief="sunken", anchor="w")
        self.status_label.pack(side="bottom", fill="x")
        
    def create_artnet_listener_tab(self, notebook):
        """创建Art-Net监听选项卡"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📡 Art-Net监听")
        
        # 控制区域
        control_frame = ttk.LabelFrame(frame, text="监听控制")
        control_frame.pack(fill="x", padx=10, pady=5)
        
        # 监听按钮
        self.listen_btn = tk.Button(control_frame, text="🎧 开始监听", 
                                   command=self.toggle_listening, bg="lightgreen")
        self.listen_btn.pack(side="left", padx=5, pady=5)
        
        # 清除按钮
        clear_btn = tk.Button(control_frame, text="🗑️ 清除", command=self.clear_log)
        clear_btn.pack(side="left", padx=5, pady=5)
        
        # 统计信息
        self.stats_label = tk.Label(control_frame, text="数据包: 0")
        self.stats_label.pack(side="right", padx=5, pady=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(frame, text="Art-Net数据包日志")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=15, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
    def create_artnet_sender_tab(self, notebook):
        """创建Art-Net发送选项卡"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📤 Art-Net发送")
        
        # 配置区域
        config_frame = ttk.LabelFrame(frame, text="发送配置")
        config_frame.pack(fill="x", padx=10, pady=5)
        
        # IP地址
        tk.Label(config_frame, text="目标IP:").grid(row=0, column=0, padx=5, pady=2, sticky="w")
        self.target_ip = tk.StringVar(value="*********")
        ip_entry = tk.Entry(config_frame, textvariable=self.target_ip, width=15)
        ip_entry.grid(row=0, column=1, padx=5, pady=2)
        
        # Universe
        tk.Label(config_frame, text="Universe:").grid(row=0, column=2, padx=5, pady=2, sticky="w")
        self.universe = tk.StringVar(value="0")
        universe_entry = tk.Entry(config_frame, textvariable=self.universe, width=5)
        universe_entry.grid(row=0, column=3, padx=5, pady=2)
        
        # 控制按钮
        self.send_btn = tk.Button(config_frame, text="🚀 开始发送", 
                                 command=self.toggle_sending, bg="lightblue")
        self.send_btn.grid(row=0, column=4, padx=10, pady=2)
        
        # DMX通道控制
        dmx_frame = ttk.LabelFrame(frame, text="DMX通道控制 (前8个通道)")
        dmx_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建8个滑块
        self.dmx_vars = []
        self.dmx_labels = []
        
        for i in range(8):
            # 通道框架
            ch_frame = tk.Frame(dmx_frame)
            ch_frame.pack(fill="x", padx=5, pady=2)
            
            # 通道标签
            tk.Label(ch_frame, text=f"Ch{i+1:02d}:", width=5).pack(side="left")
            
            # 滑块
            var = tk.IntVar(value=0)
            scale = tk.Scale(ch_frame, from_=0, to=255, orient="horizontal", 
                           variable=var, length=300)
            scale.pack(side="left", fill="x", expand=True, padx=5)
            
            # 数值标签
            value_label = tk.Label(ch_frame, text="0", width=4)
            value_label.pack(side="right")
            
            # 绑定更新事件
            var.trace("w", lambda *args, idx=i: self.update_dmx_display(idx))
            
            self.dmx_vars.append(var)
            self.dmx_labels.append(value_label)
            
        # 预设按钮
        preset_frame = tk.Frame(dmx_frame)
        preset_frame.pack(fill="x", padx=5, pady=5)
        
        presets = [
            ("全零", [0]*8),
            ("全满", [255]*8),
            ("测试", [255, 128, 64, 32, 16, 8, 4, 2])
        ]
        
        for name, values in presets:
            btn = tk.Button(preset_frame, text=name, 
                          command=lambda v=values: self.apply_preset(v))
            btn.pack(side="left", padx=5)
            
    def create_timecode_tab(self, notebook):
        """创建时间码测试选项卡"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="🕐 时间码测试")
        
        # MIDI时间码
        midi_frame = ttk.LabelFrame(frame, text="MIDI时间码测试")
        midi_frame.pack(fill="x", padx=10, pady=5)
        
        # 时间码输入
        tc_frame = tk.Frame(midi_frame)
        tc_frame.pack(pady=5)
        
        tk.Label(tc_frame, text="时间码:").pack(side="left", padx=5)
        
        self.hours = tk.StringVar(value="00")
        self.minutes = tk.StringVar(value="00")
        self.seconds = tk.StringVar(value="10")
        self.frames = tk.StringVar(value="00")
        
        tk.Entry(tc_frame, textvariable=self.hours, width=3).pack(side="left", padx=2)
        tk.Label(tc_frame, text=":").pack(side="left")
        tk.Entry(tc_frame, textvariable=self.minutes, width=3).pack(side="left", padx=2)
        tk.Label(tc_frame, text=":").pack(side="left")
        tk.Entry(tc_frame, textvariable=self.seconds, width=3).pack(side="left", padx=2)
        tk.Label(tc_frame, text=":").pack(side="left")
        tk.Entry(tc_frame, textvariable=self.frames, width=3).pack(side="left", padx=2)
        
        # 发送按钮
        tk.Button(tc_frame, text="📤 发送MIDI时间码", 
                 command=self.send_midi_timecode).pack(side="left", padx=10)
        
        # 网络时间码
        net_frame = ttk.LabelFrame(frame, text="网络时间码测试")
        net_frame.pack(fill="x", padx=10, pady=5)
        
        # 网络时间码按钮
        net_btn_frame = tk.Frame(net_frame)
        net_btn_frame.pack(pady=5)
        
        tk.Button(net_btn_frame, text="📡 发送SMPTE", 
                 command=self.send_smpte_timecode).pack(side="left", padx=5)
        tk.Button(net_btn_frame, text="🌐 发送OSC", 
                 command=self.send_osc_timecode).pack(side="left", padx=5)
        
        # 测试结果
        self.test_result = tk.Text(frame, height=8, font=("Consolas", 9))
        self.test_result.pack(fill="both", expand=True, padx=10, pady=5)
        
    def toggle_listening(self):
        """切换监听状态"""
        if not self.listening:
            self.start_listening()
        else:
            self.stop_listening()
            
    def start_listening(self):
        """开始监听Art-Net"""
        try:
            self.listening = True
            self.listen_btn.config(text="⏹️ 停止监听", bg="lightcoral")
            self.status_label.config(text="正在监听Art-Net数据包...")
            
            # 启动监听线程
            self.listen_thread = threading.Thread(target=self.listen_artnet, daemon=True)
            self.listen_thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动监听失败: {e}")
            self.listening = False
            
    def stop_listening(self):
        """停止监听"""
        self.listening = False
        self.listen_btn.config(text="🎧 开始监听", bg="lightgreen")
        self.status_label.config(text="监听已停止")
        
    def listen_artnet(self):
        """Art-Net监听线程"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.bind(('', 6454))
            sock.settimeout(1.0)
            
            while self.listening:
                try:
                    data, addr = sock.recvfrom(1024)
                    if len(data) >= 18 and data[:8] == b"Art-Net\x00":
                        self.packet_count += 1
                        self.process_artnet_packet(data, addr)
                        
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.listening:
                        self.log_message(f"❌ 接收错误: {e}")
                        
            sock.close()
            
        except Exception as e:
            self.log_message(f"❌ 监听失败: {e}")
            
    def process_artnet_packet(self, data, addr):
        """处理Art-Net数据包"""
        try:
            # 解析Art-Net包
            opcode = struct.unpack("<H", data[8:10])[0]
            
            if opcode == 0x5000:  # OpDmx
                universe = data[14]
                dmx_data = data[18:18+512] if len(data) >= 530 else data[18:]
                
                # 显示前8个通道的值
                channels = [dmx_data[i] if i < len(dmx_data) else 0 for i in range(8)]
                channel_str = " ".join([f"{i+1}:{v:3d}" for i, v in enumerate(channels)])
                
                timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                message = f"[{timestamp}] 📦 来自 {addr[0]} Universe:{universe} 通道: {channel_str}"
                
                self.log_message(message)
                
                # 更新统计
                self.root.after(0, lambda: self.stats_label.config(text=f"数据包: {self.packet_count}"))
                
        except Exception as e:
            self.log_message(f"❌ 解析错误: {e}")
            
    def log_message(self, message):
        """添加日志消息"""
        def update_log():
            self.log_text.insert("end", message + "\n")
            self.log_text.see("end")
            
            # 限制日志行数
            lines = int(self.log_text.index("end-1c").split(".")[0])
            if lines > 1000:
                self.log_text.delete("1.0", "100.0")
                
        self.root.after(0, update_log)
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", "end")
        self.packet_count = 0
        self.stats_label.config(text="数据包: 0")
        
    def toggle_sending(self):
        """切换发送状态"""
        if not self.sending:
            self.start_sending()
        else:
            self.stop_sending()
            
    def start_sending(self):
        """开始发送Art-Net"""
        try:
            self.sending = True
            self.send_btn.config(text="⏹️ 停止发送", bg="lightcoral")
            self.status_label.config(text="正在发送Art-Net数据...")
            
            # 启动发送线程
            self.send_thread = threading.Thread(target=self.send_artnet, daemon=True)
            self.send_thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动发送失败: {e}")
            self.sending = False
            
    def stop_sending(self):
        """停止发送"""
        self.sending = False
        self.send_btn.config(text="🚀 开始发送", bg="lightblue")
        self.status_label.config(text="发送已停止")
        
    def send_artnet(self):
        """Art-Net发送线程"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            
            sequence = 0
            
            while self.sending:
                try:
                    # 创建Art-Net包
                    packet = self.create_artnet_packet()
                    
                    # 发送到目标IP
                    target = self.target_ip.get() or "***************"
                    sock.sendto(packet, (target, 6454))
                    
                    sequence = (sequence + 1) % 256
                    time.sleep(1/40)  # 40Hz
                    
                except Exception as e:
                    if self.sending:
                        print(f"发送错误: {e}")
                        time.sleep(0.1)
                        
            sock.close()
            
        except Exception as e:
            print(f"发送线程错误: {e}")
            
    def create_artnet_packet(self):
        """创建Art-Net数据包"""
        packet = bytearray()
        packet.extend(b"Art-Net\x00")  # Art-Net ID
        packet.extend(struct.pack("<H", 0x5000))  # OpDmx
        packet.extend(struct.pack(">H", 14))  # Protocol version
        packet.append(0)  # Sequence
        packet.append(0)  # Physical
        
        universe = int(self.universe.get() or "0")
        packet.append(universe & 0xFF)  # Universe low
        packet.append((universe >> 8) & 0xFF)  # Universe high
        packet.extend(struct.pack(">H", 512))  # Data length
        
        # DMX数据
        dmx_data = [0] * 512
        for i, var in enumerate(self.dmx_vars):
            if i < len(dmx_data):
                dmx_data[i] = var.get()
                
        packet.extend(dmx_data)
        return packet
        
    def update_dmx_display(self, channel_idx):
        """更新DMX显示"""
        if channel_idx < len(self.dmx_labels):
            value = self.dmx_vars[channel_idx].get()
            self.dmx_labels[channel_idx].config(text=str(value))
            
    def apply_preset(self, values):
        """应用预设值"""
        for i, value in enumerate(values):
            if i < len(self.dmx_vars):
                self.dmx_vars[i].set(value)
                
    def send_midi_timecode(self):
        """发送MIDI时间码"""
        try:
            import pygame.midi
            
            if not pygame.midi.get_init():
                pygame.midi.init()
                
            device_count = pygame.midi.get_count()
            if device_count == 0:
                self.test_result.insert("end", "❌ 未找到MIDI设备\n")
                return
                
            # 获取时间码值
            h = int(self.hours.get() or "0")
            m = int(self.minutes.get() or "0")
            s = int(self.seconds.get() or "0")
            f = int(self.frames.get() or "0")
            
            # 发送到第一个输出设备
            for i in range(device_count):
                info = pygame.midi.get_device_info(i)
                if info[3]:  # 输出设备
                    try:
                        midi_out = pygame.midi.Output(i)
                        
                        # MTC Full Frame
                        mtc_data = [0xF0, 0x7F, 0x7F, 0x01, 0x01, h, m, s, f, 0xF7]
                        for byte in mtc_data:
                            midi_out.write_short(byte)
                            
                        midi_out.close()
                        
                        timestamp = datetime.now().strftime("%H:%M:%S")
                        self.test_result.insert("end", 
                            f"[{timestamp}] ✅ MIDI时间码已发送: {h:02d}:{m:02d}:{s:02d}:{f:02d} "
                            f"到设备 {info[1].decode()}\n")
                        break
                    except Exception as e:
                        self.test_result.insert("end", f"❌ MIDI发送失败: {e}\n")
                        
            pygame.midi.quit()
            self.test_result.see("end")
            
        except ImportError:
            self.test_result.insert("end", "❌ pygame.midi不可用\n")
        except Exception as e:
            self.test_result.insert("end", f"❌ MIDI时间码发送失败: {e}\n")
            
    def send_smpte_timecode(self):
        """发送SMPTE时间码"""
        try:
            h = self.hours.get() or "00"
            m = self.minutes.get() or "00"
            s = self.seconds.get() or "10"
            f = self.frames.get() or "00"
            
            smpte_data = f"SMPTE:{h}:{m}:{s}:{f}"
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            targets = [
                (self.target_ip.get() or "127.0.0.1", 8000),
                ("127.0.0.1", 8000)
            ]
            
            for target_ip, port in targets:
                try:
                    sock.sendto(smpte_data.encode(), (target_ip, port))
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.test_result.insert("end", 
                        f"[{timestamp}] ✅ SMPTE时间码已发送到 {target_ip}:{port} - {smpte_data}\n")
                except Exception as e:
                    self.test_result.insert("end", f"❌ SMPTE发送到 {target_ip}:{port} 失败: {e}\n")
                    
            sock.close()
            self.test_result.see("end")
            
        except Exception as e:
            self.test_result.insert("end", f"❌ SMPTE时间码发送失败: {e}\n")
            
    def send_osc_timecode(self):
        """发送OSC时间码"""
        try:
            h = self.hours.get() or "00"
            m = self.minutes.get() or "00"
            s = self.seconds.get() or "10"
            f = self.frames.get() or "00"
            
            osc_data = f"/timecode {h} {m} {s} {f}"
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            targets = [
                (self.target_ip.get() or "127.0.0.1", 9000),
                ("127.0.0.1", 9000)
            ]
            
            for target_ip, port in targets:
                try:
                    sock.sendto(osc_data.encode(), (target_ip, port))
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.test_result.insert("end", 
                        f"[{timestamp}] ✅ OSC时间码已发送到 {target_ip}:{port} - {osc_data}\n")
                except Exception as e:
                    self.test_result.insert("end", f"❌ OSC发送到 {target_ip}:{port} 失败: {e}\n")
                    
            sock.close()
            self.test_result.see("end")
            
        except Exception as e:
            self.test_result.insert("end", f"❌ OSC时间码发送失败: {e}\n")
            
    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MA2TestTool()
    app.run()
