<?xml version="1.0" encoding="utf-8"?>
<MA xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" major_vers="3" minor_vers="9" stream_vers="0">
  <Info>
    <FixtureType Name="acme@aeco_20@37channel" LongName="ACME AECO 20 LED Moving Head" Manufacturer="ACME">
      <Physical Power="300W" Weight="18.5kg" Width="380" Height="520" Depth="350" 
                PanRange="540" TiltRange="270" BeamAngle="7-50" LuminousFlux="12000"/>
      
      <!-- 37通道模式 -->
      <Mode Name="37CH" PhysicalFrom="1" PhysicalTo="37">
        <Channel Geometry="Pan" Feature="Pan" Resolution="16Bit">
          <ChannelFunction Attribute="Pan" OriginalAttribute="Pan" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="540" RealFade="2.80" RealAcceleration="1.20"/>
        </Channel>
        <Channel Geometry="Pan" Feature="Pan_fine" Resolution="16Bit">
          <ChannelFunction Attribute="Pan_fine" OriginalAttribute="Pan_fine" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="540"/>
        </Channel>
        <Channel Geometry="Tilt" Feature="Tilt" Resolution="16Bit">
          <ChannelFunction Attribute="Tilt" OriginalAttribute="Tilt" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="270" RealFade="2.80" RealAcceleration="1.20"/>
        </Channel>
        <Channel Geometry="Tilt" Feature="Tilt_fine" Resolution="16Bit">
          <ChannelFunction Attribute="Tilt_fine" OriginalAttribute="Tilt_fine" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="270"/>
        </Channel>
        <Channel Geometry="Dimmer" Feature="Dimmer" Resolution="8Bit">
          <ChannelFunction Attribute="Dimmer" OriginalAttribute="Dimmer" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1" RealFade="3.00"/>
        </Channel>
        <Channel Geometry="Shutter" Feature="Shutter_n_Strobe" Resolution="8Bit">
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="0" DMXTo="31" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="32" DMXTo="63" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="64" DMXTo="95" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="96" DMXTo="127" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="128" DMXTo="159" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="160" DMXTo="191" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="192" DMXTo="223" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="224" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit" Highlight="255">
          <ChannelFunction Attribute="Red" OriginalAttribute="Red" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit" Highlight="255">
          <ChannelFunction Attribute="Green" OriginalAttribute="Green" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit" Highlight="255">
          <ChannelFunction Attribute="Blue" OriginalAttribute="Blue" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit" Highlight="255">
          <ChannelFunction Attribute="White" OriginalAttribute="White" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit" Highlight="255">
          <ChannelFunction Attribute="Amber" OriginalAttribute="Amber" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit" Highlight="255">
          <ChannelFunction Attribute="UV" OriginalAttribute="UV" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="ColorWheel" Feature="ColorWheel" Resolution="8Bit">
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="0" DMXTo="9" PhysicalFrom="0" PhysicalTo="1" Color="0xffffff"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="10" DMXTo="19" PhysicalFrom="0" PhysicalTo="1" Color="0xff0000"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="20" DMXTo="29" PhysicalFrom="0" PhysicalTo="1" Color="0x00ff00"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="30" DMXTo="39" PhysicalFrom="0" PhysicalTo="1" Color="0x0000ff"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="40" DMXTo="49" PhysicalFrom="0" PhysicalTo="1" Color="0xffff00"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="50" DMXTo="59" PhysicalFrom="0" PhysicalTo="1" Color="0xff00ff"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="60" DMXTo="69" PhysicalFrom="0" PhysicalTo="1" Color="0x00ffff"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="70" DMXTo="79" PhysicalFrom="0" PhysicalTo="1" Color="0xffa500"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="128" DMXTo="189" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="190" DMXTo="193" PhysicalFrom="0" PhysicalTo="0"/>
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="194" DMXTo="255" PhysicalFrom="0.5" PhysicalTo="25"/>
        </Channel>
        <Channel Geometry="Gobo" Feature="Gobo" Resolution="8Bit">
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="0" DMXTo="9" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="10" DMXTo="19" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="20" DMXTo="29" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="30" DMXTo="39" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="40" DMXTo="49" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="50" DMXTo="59" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="60" DMXTo="69" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="70" DMXTo="89" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="90" DMXTo="109" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="110" DMXTo="129" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="130" DMXTo="149" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="150" DMXTo="169" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="170" DMXTo="189" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="190" DMXTo="221" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="222" DMXTo="255" PhysicalFrom="-25" PhysicalTo="-0.5"/>
        </Channel>
        <Channel Geometry="Gobo" Feature="GoboRot" Resolution="8Bit">
          <ChannelFunction Attribute="Gobo1Rot" OriginalAttribute="Gobo1Rot" DMXFrom="0" DMXTo="127" PhysicalFrom="0" PhysicalTo="360"/>
          <ChannelFunction Attribute="Gobo1Rot" OriginalAttribute="Gobo1Rot" DMXFrom="128" DMXTo="189" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Gobo1Rot" OriginalAttribute="Gobo1Rot" DMXFrom="190" DMXTo="193" PhysicalFrom="0" PhysicalTo="0"/>
          <ChannelFunction Attribute="Gobo1Rot" OriginalAttribute="Gobo1Rot" DMXFrom="194" DMXTo="255" PhysicalFrom="-25" PhysicalTo="-0.5"/>
        </Channel>
        <Channel Geometry="Gobo" Feature="Gobo" Resolution="8Bit">
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="0" DMXTo="9" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="10" DMXTo="19" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="20" DMXTo="29" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="30" DMXTo="39" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="40" DMXTo="49" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="50" DMXTo="59" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="60" DMXTo="69" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="128" DMXTo="189" PhysicalFrom="-1" PhysicalTo="1"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="190" DMXTo="221" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Gobo2" OriginalAttribute="Gobo2" DMXFrom="222" DMXTo="255" PhysicalFrom="-25" PhysicalTo="-0.5"/>
        </Channel>
        <Channel Geometry="Prism" Feature="Prism" Resolution="8Bit">
          <ChannelFunction Attribute="Prism1" OriginalAttribute="Prism1" DMXFrom="0" DMXTo="127" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Prism1" OriginalAttribute="Prism1" DMXFrom="128" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Prism" Feature="PrismRot" Resolution="8Bit">
          <ChannelFunction Attribute="Prism1Rot" OriginalAttribute="Prism1Rot" DMXFrom="0" DMXTo="127" PhysicalFrom="0" PhysicalTo="360"/>
          <ChannelFunction Attribute="Prism1Rot" OriginalAttribute="Prism1Rot" DMXFrom="128" DMXTo="189" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Prism1Rot" OriginalAttribute="Prism1Rot" DMXFrom="190" DMXTo="193" PhysicalFrom="0" PhysicalTo="0"/>
          <ChannelFunction Attribute="Prism1Rot" OriginalAttribute="Prism1Rot" DMXFrom="194" DMXTo="255" PhysicalFrom="-25" PhysicalTo="-0.5"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Focus" Resolution="8Bit">
          <ChannelFunction Attribute="Focus1" OriginalAttribute="Focus1" DMXFrom="0" DMXTo="255" PhysicalFrom="1" PhysicalTo="0"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Zoom" Resolution="8Bit">
          <ChannelFunction Attribute="Zoom" OriginalAttribute="Zoom" DMXFrom="0" DMXTo="255" PhysicalFrom="7" PhysicalTo="50"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Iris" Resolution="8Bit">
          <ChannelFunction Attribute="Iris1" OriginalAttribute="Iris1" DMXFrom="0" DMXTo="191" PhysicalFrom="1" PhysicalTo="0"/>
          <ChannelFunction Attribute="Iris1" OriginalAttribute="Iris1" DMXFrom="192" DMXTo="223" PhysicalFrom="0.5" PhysicalTo="25"/>
          <ChannelFunction Attribute="Iris1" OriginalAttribute="Iris1" DMXFrom="224" DMXTo="255" PhysicalFrom="-25" PhysicalTo="-0.5"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Frost" Resolution="8Bit">
          <ChannelFunction Attribute="Frost1" OriginalAttribute="Frost1" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Control" Feature="Control" Resolution="8Bit">
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="0" DMXTo="9" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="10" DMXTo="14" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="15" DMXTo="19" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="20" DMXTo="24" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="25" DMXTo="29" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="30" DMXTo="34" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="35" DMXTo="39" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="40" DMXTo="44" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="45" DMXTo="49" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="50" DMXTo="54" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="55" DMXTo="59" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="60" DMXTo="64" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="65" DMXTo="69" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="70" DMXTo="74" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="75" DMXTo="79" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="80" DMXTo="84" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="85" DMXTo="87" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="88" DMXTo="90" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="91" DMXTo="93" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="94" DMXTo="96" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="97" DMXTo="99" PhysicalFrom="0" PhysicalTo="1"/>
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="100" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <!-- 继续添加剩余通道... -->
        <Channel Geometry="Maintenance" Feature="Maintenance" Resolution="8Bit">
          <ChannelFunction Attribute="Maintenance" OriginalAttribute="Maintenance" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
      </Mode>
      
      <!-- 简化模式 -->
      <Mode Name="16CH" PhysicalFrom="1" PhysicalTo="16">
        <Channel Geometry="Pan" Feature="Pan" Resolution="8Bit">
          <ChannelFunction Attribute="Pan" OriginalAttribute="Pan" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="540"/>
        </Channel>
        <Channel Geometry="Tilt" Feature="Tilt" Resolution="8Bit">
          <ChannelFunction Attribute="Tilt" OriginalAttribute="Tilt" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="270"/>
        </Channel>
        <Channel Geometry="Dimmer" Feature="Dimmer" Resolution="8Bit">
          <ChannelFunction Attribute="Dimmer" OriginalAttribute="Dimmer" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Shutter" Feature="Shutter_n_Strobe" Resolution="8Bit">
          <ChannelFunction Attribute="Shutter_n_Strobe" OriginalAttribute="Shutter_n_Strobe" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="25"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit">
          <ChannelFunction Attribute="Red" OriginalAttribute="Red" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit">
          <ChannelFunction Attribute="Green" OriginalAttribute="Green" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit">
          <ChannelFunction Attribute="Blue" OriginalAttribute="Blue" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Color" Feature="ColorMixing" Resolution="8Bit">
          <ChannelFunction Attribute="White" OriginalAttribute="White" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="ColorWheel" Feature="ColorWheel" Resolution="8Bit">
          <ChannelFunction Attribute="ColorWheel1" OriginalAttribute="ColorWheel1" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Gobo" Feature="Gobo" Resolution="8Bit">
          <ChannelFunction Attribute="Gobo1" OriginalAttribute="Gobo1" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Gobo" Feature="GoboRot" Resolution="8Bit">
          <ChannelFunction Attribute="Gobo1Rot" OriginalAttribute="Gobo1Rot" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="360"/>
        </Channel>
        <Channel Geometry="Prism" Feature="Prism" Resolution="8Bit">
          <ChannelFunction Attribute="Prism1" OriginalAttribute="Prism1" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Focus" Resolution="8Bit">
          <ChannelFunction Attribute="Focus1" OriginalAttribute="Focus1" DMXFrom="0" DMXTo="255" PhysicalFrom="1" PhysicalTo="0"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Zoom" Resolution="8Bit">
          <ChannelFunction Attribute="Zoom" OriginalAttribute="Zoom" DMXFrom="0" DMXTo="255" PhysicalFrom="7" PhysicalTo="50"/>
        </Channel>
        <Channel Geometry="Beam" Feature="Iris" Resolution="8Bit">
          <ChannelFunction Attribute="Iris1" OriginalAttribute="Iris1" DMXFrom="0" DMXTo="255" PhysicalFrom="1" PhysicalTo="0"/>
        </Channel>
        <Channel Geometry="Control" Feature="Control" Resolution="8Bit">
          <ChannelFunction Attribute="Control1" OriginalAttribute="Control1" DMXFrom="0" DMXTo="255" PhysicalFrom="0" PhysicalTo="1"/>
        </Channel>
      </Mode>
    </FixtureType>
  </Info>
</MA>
