#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MA控台XML灯具库解析器
支持GrandMA2/MA3的XML灯具库格式
"""

import xml.etree.ElementTree as ET
import json
import os
from typing import Dict, List, Optional, Tuple

class MAFixtureParser:
    """MA控台XML灯具库解析器"""
    
    def __init__(self):
        self.fixtures = {}
        self.fixture_types = {}
        
    def parse_ma_xml_library(self, xml_file_path: str) -> bool:
        """解析MA控台XML灯具库文件"""
        try:
            print(f"🔍 正在解析MA XML灯具库: {xml_file_path}")
            
            # 解析XML文件
            tree = ET.parse(xml_file_path)
            root = tree.getroot()
            
            # 检查XML格式
            if root.tag == "MA":
                # MA2/MA3格式
                return self._parse_ma_format(root)
            elif root.tag == "FixtureLibrary":
                # 通用灯具库格式
                return self._parse_generic_format(root)
            else:
                print(f"❌ 不支持的XML格式: {root.tag}")
                return False
                
        except ET.ParseError as e:
            print(f"❌ XML解析错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 解析失败: {e}")
            return False
    
    def _parse_ma_format(self, root: ET.Element) -> bool:
        """解析MA格式的XML"""
        try:
            # 查找灯具定义
            fixtures_found = 0
            
            # MA2格式: <MA><Info><FixtureType>
            for fixture_type in root.findall(".//FixtureType"):
                fixture_data = self._parse_fixture_type(fixture_type)
                if fixture_data:
                    self.fixtures[fixture_data['id']] = fixture_data
                    fixtures_found += 1
            
            # MA3格式: <MA><FixtureTypes><FixtureType>
            for fixture_type in root.findall(".//FixtureTypes/FixtureType"):
                fixture_data = self._parse_fixture_type(fixture_type)
                if fixture_data:
                    self.fixtures[fixture_data['id']] = fixture_data
                    fixtures_found += 1
            
            print(f"✅ 成功解析 {fixtures_found} 个灯具类型")
            return fixtures_found > 0
            
        except Exception as e:
            print(f"❌ MA格式解析失败: {e}")
            return False
    
    def _parse_generic_format(self, root: ET.Element) -> bool:
        """解析通用格式的XML"""
        try:
            fixtures_found = 0
            
            for fixture in root.findall(".//Fixture"):
                fixture_data = self._parse_generic_fixture(fixture)
                if fixture_data:
                    self.fixtures[fixture_data['id']] = fixture_data
                    fixtures_found += 1
            
            print(f"✅ 成功解析 {fixtures_found} 个灯具类型")
            return fixtures_found > 0
            
        except Exception as e:
            print(f"❌ 通用格式解析失败: {e}")
            return False
    
    def _parse_fixture_type(self, fixture_element: ET.Element) -> Optional[Dict]:
        """解析单个灯具类型"""
        try:
            # 基本信息
            fixture_id = fixture_element.get('Name') or fixture_element.get('name')
            if not fixture_id:
                return None
            
            # 制造商信息
            manufacturer = fixture_element.get('Manufacturer') or fixture_element.get('manufacturer', '未知制造商')
            
            # 灯具名称
            fixture_name = fixture_element.get('LongName') or fixture_element.get('longname') or fixture_id
            
            # 解析通道信息
            channels = self._parse_channels(fixture_element)
            
            # 解析模式信息
            modes = self._parse_modes(fixture_element)
            
            # 解析物理属性
            physical = self._parse_physical_properties(fixture_element)
            
            # 解析颜色轮
            color_wheels = self._parse_color_wheels(fixture_element)
            
            # 解析图案轮
            gobo_wheels = self._parse_gobo_wheels(fixture_element)
            
            fixture_data = {
                'id': fixture_id,
                'name': fixture_name,
                'manufacturer': manufacturer,
                'channels': channels,
                'modes': modes,
                'physical': physical,
                'color_wheels': color_wheels,
                'gobo_wheels': gobo_wheels,
                'channel_count': len(channels),
                'source': 'MA_XML'
            }
            
            return fixture_data
            
        except Exception as e:
            print(f"❌ 解析灯具类型失败: {e}")
            return None
    
    def _parse_generic_fixture(self, fixture_element: ET.Element) -> Optional[Dict]:
        """解析通用格式的灯具"""
        try:
            fixture_id = fixture_element.get('id') or fixture_element.get('name')
            if not fixture_id:
                return None
            
            manufacturer = fixture_element.get('manufacturer', '未知制造商')
            fixture_name = fixture_element.get('name') or fixture_id
            
            # 解析通道
            channels = {}
            for i, channel in enumerate(fixture_element.findall('.//Channel')):
                channel_name = channel.get('name') or f"Channel_{i+1}"
                channel_function = channel.get('function') or 'Dimmer'
                
                channels[channel_name.lower()] = {
                    'channel': i + 1,
                    'function': channel_function,
                    'range': [0, 255]
                }
            
            return {
                'id': fixture_id,
                'name': fixture_name,
                'manufacturer': manufacturer,
                'channels': channels,
                'modes': {'default': {'channels': list(channels.keys())}},
                'physical': {},
                'color_wheels': [],
                'gobo_wheels': [],
                'channel_count': len(channels),
                'source': 'Generic_XML'
            }
            
        except Exception as e:
            print(f"❌ 解析通用灯具失败: {e}")
            return None
    
    def _parse_channels(self, fixture_element: ET.Element) -> Dict:
        """解析通道信息"""
        channels = {}
        
        try:
            # MA格式的通道定义
            for channel in fixture_element.findall('.//Channel'):
                channel_info = self._parse_single_channel(channel)
                if channel_info:
                    channels[channel_info['name']] = channel_info
            
            # 如果没找到Channel标签，尝试其他可能的标签
            if not channels:
                for attr in fixture_element.findall('.//Attribute'):
                    attr_info = self._parse_attribute(attr)
                    if attr_info:
                        channels[attr_info['name']] = attr_info
            
        except Exception as e:
            print(f"❌ 解析通道失败: {e}")
        
        return channels
    
    def _parse_single_channel(self, channel_element: ET.Element) -> Optional[Dict]:
        """解析单个通道"""
        try:
            # 通道名称
            name = (channel_element.get('Name') or 
                   channel_element.get('name') or 
                   channel_element.text or 
                   'Unknown').lower()
            
            # 通道功能
            function = channel_element.get('Function') or channel_element.get('function', 'Dimmer')
            
            # 通道范围
            min_val = int(channel_element.get('Min', 0))
            max_val = int(channel_element.get('Max', 255))
            
            # 默认值
            default_val = int(channel_element.get('Default', 0))
            
            return {
                'name': name,
                'function': function,
                'range': [min_val, max_val],
                'default': default_val,
                'resolution': channel_element.get('Resolution', '8bit')
            }
            
        except Exception as e:
            print(f"❌ 解析单个通道失败: {e}")
            return None
    
    def _parse_attribute(self, attr_element: ET.Element) -> Optional[Dict]:
        """解析属性（MA3格式）"""
        try:
            name = (attr_element.get('Name') or 
                   attr_element.get('name', 'Unknown')).lower()
            
            feature = attr_element.get('Feature', 'Dimmer')
            
            return {
                'name': name,
                'function': feature,
                'range': [0, 255],
                'default': 0,
                'resolution': '8bit'
            }
            
        except Exception as e:
            print(f"❌ 解析属性失败: {e}")
            return None
    
    def _parse_modes(self, fixture_element: ET.Element) -> Dict:
        """解析模式信息"""
        modes = {}
        
        try:
            # 查找模式定义
            for mode in fixture_element.findall('.//Mode'):
                mode_name = mode.get('Name') or mode.get('name', 'Default')
                mode_channels = []
                
                # 解析模式中的通道
                for channel_ref in mode.findall('.//Channel'):
                    channel_name = channel_ref.get('Name') or channel_ref.get('name')
                    if channel_name:
                        mode_channels.append(channel_name.lower())
                
                if mode_channels:
                    modes[mode_name] = {
                        'channels': mode_channels,
                        'channel_count': len(mode_channels)
                    }
            
            # 如果没有找到模式，创建默认模式
            if not modes:
                modes['default'] = {
                    'channels': ['dimmer', 'pan', 'tilt', 'color', 'gobo'],
                    'channel_count': 5
                }
                
        except Exception as e:
            print(f"❌ 解析模式失败: {e}")
        
        return modes
    
    def _parse_physical_properties(self, fixture_element: ET.Element) -> Dict:
        """解析物理属性"""
        physical = {}
        
        try:
            # 查找物理属性
            phys_element = fixture_element.find('.//Physical')
            if phys_element is not None:
                # 功率
                power = phys_element.get('Power')
                if power:
                    physical['power'] = int(power.replace('W', ''))
                
                # 重量
                weight = phys_element.get('Weight')
                if weight:
                    physical['weight'] = float(weight.replace('kg', ''))
                
                # 尺寸
                width = phys_element.get('Width')
                height = phys_element.get('Height')
                depth = phys_element.get('Depth')
                
                if width and height and depth:
                    physical['dimensions'] = {
                        'width': float(width),
                        'height': float(height),
                        'depth': float(depth)
                    }
                
                # 光束角度
                beam_angle = phys_element.get('BeamAngle')
                if beam_angle:
                    physical['beam_angle'] = float(beam_angle)
                
                # 旋转范围
                pan_range = phys_element.get('PanRange')
                tilt_range = phys_element.get('TiltRange')
                
                if pan_range:
                    physical['pan_range'] = float(pan_range)
                if tilt_range:
                    physical['tilt_range'] = float(tilt_range)
            
        except Exception as e:
            print(f"❌ 解析物理属性失败: {e}")
        
        return physical
    
    def _parse_color_wheels(self, fixture_element: ET.Element) -> List[Dict]:
        """解析颜色轮"""
        color_wheels = []
        
        try:
            for wheel in fixture_element.findall('.//ColorWheel'):
                wheel_data = {
                    'name': wheel.get('Name', 'Color Wheel'),
                    'colors': []
                }
                
                for color in wheel.findall('.//Color'):
                    color_name = color.get('Name') or color.text
                    color_value = color.get('Value', '0')
                    
                    if color_name:
                        wheel_data['colors'].append({
                            'name': color_name,
                            'value': int(color_value),
                            'rgb': color.get('RGB', '#FFFFFF')
                        })
                
                if wheel_data['colors']:
                    color_wheels.append(wheel_data)
                    
        except Exception as e:
            print(f"❌ 解析颜色轮失败: {e}")
        
        return color_wheels
    
    def _parse_gobo_wheels(self, fixture_element: ET.Element) -> List[Dict]:
        """解析图案轮"""
        gobo_wheels = []
        
        try:
            for wheel in fixture_element.findall('.//GoboWheel'):
                wheel_data = {
                    'name': wheel.get('Name', 'Gobo Wheel'),
                    'gobos': []
                }
                
                for gobo in wheel.findall('.//Gobo'):
                    gobo_name = gobo.get('Name') or gobo.text
                    gobo_value = gobo.get('Value', '0')
                    
                    if gobo_name:
                        wheel_data['gobos'].append({
                            'name': gobo_name,
                            'value': int(gobo_value),
                            'image': gobo.get('Image', '')
                        })
                
                if wheel_data['gobos']:
                    gobo_wheels.append(wheel_data)
                    
        except Exception as e:
            print(f"❌ 解析图案轮失败: {e}")
        
        return gobo_wheels
    
    def export_to_json(self, output_file: str) -> bool:
        """导出为JSON格式"""
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.fixtures, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 已导出到JSON文件: {output_file}")
            return True
            
        except Exception as e:
            print(f"❌ 导出JSON失败: {e}")
            return False
    
    def get_fixture_by_manufacturer(self, manufacturer: str) -> List[Dict]:
        """按制造商获取灯具"""
        return [fixture for fixture in self.fixtures.values() 
                if fixture['manufacturer'].lower() == manufacturer.lower()]
    
    def search_fixtures(self, keyword: str) -> List[Dict]:
        """搜索灯具"""
        keyword = keyword.lower()
        results = []
        
        for fixture in self.fixtures.values():
            if (keyword in fixture['name'].lower() or 
                keyword in fixture['manufacturer'].lower() or
                keyword in fixture['id'].lower()):
                results.append(fixture)
        
        return results
    
    def get_fixture_info(self, fixture_id: str) -> Optional[Dict]:
        """获取灯具信息"""
        return self.fixtures.get(fixture_id)
    
    def list_all_fixtures(self) -> List[str]:
        """列出所有灯具ID"""
        return list(self.fixtures.keys())
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        manufacturers = set(fixture['manufacturer'] for fixture in self.fixtures.values())
        
        channel_counts = {}
        for fixture in self.fixtures.values():
            count = fixture['channel_count']
            channel_counts[count] = channel_counts.get(count, 0) + 1
        
        return {
            'total_fixtures': len(self.fixtures),
            'manufacturers': list(manufacturers),
            'manufacturer_count': len(manufacturers),
            'channel_distribution': channel_counts
        }

def create_sample_ma_xml():
    """创建示例MA XML文件"""
    sample_xml = '''<?xml version="1.0" encoding="utf-8"?>
<MA xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" major_vers="1" minor_vers="0" stream_vers="0">
  <Info>
    <FixtureType Name="LED_Par_RGBW" LongName="LED Par RGBW" Manufacturer="Generic">
      <Physical Power="150W" Weight="3.5kg" Width="250" Height="320" Depth="180" BeamAngle="25"/>
      <Mode Name="4CH">
        <Channel Name="Dimmer" Function="Dimmer" Default="0"/>
        <Channel Name="Red" Function="Red" Default="0"/>
        <Channel Name="Green" Function="Green" Default="0"/>
        <Channel Name="Blue" Function="Blue" Default="0"/>
      </Mode>
      <Mode Name="6CH">
        <Channel Name="Dimmer" Function="Dimmer" Default="0"/>
        <Channel Name="Red" Function="Red" Default="0"/>
        <Channel Name="Green" Function="Green" Default="0"/>
        <Channel Name="Blue" Function="Blue" Default="0"/>
        <Channel Name="White" Function="White" Default="0"/>
        <Channel Name="Strobe" Function="Strobe" Default="0"/>
      </Mode>
    </FixtureType>
    
    <FixtureType Name="Moving_Head_Spot" LongName="Moving Head Spot Light" Manufacturer="Generic">
      <Physical Power="300W" Weight="15kg" Width="350" Height="450" Depth="300" 
                PanRange="540" TiltRange="270" BeamAngle="15"/>
      <Mode Name="16CH">
        <Channel Name="Pan" Function="Pan" Default="128"/>
        <Channel Name="Tilt" Function="Tilt" Default="128"/>
        <Channel Name="Dimmer" Function="Dimmer" Default="0"/>
        <Channel Name="Strobe" Function="Strobe" Default="0"/>
        <Channel Name="Color" Function="Color" Default="0"/>
        <Channel Name="Gobo" Function="Gobo" Default="0"/>
        <Channel Name="Prism" Function="Prism" Default="0"/>
        <Channel Name="Focus" Function="Focus" Default="128"/>
      </Mode>
      <ColorWheel Name="Color Wheel 1">
        <Color Name="Open" Value="0" RGB="#FFFFFF"/>
        <Color Name="Red" Value="32" RGB="#FF0000"/>
        <Color Name="Green" Value="64" RGB="#00FF00"/>
        <Color Name="Blue" Value="96" RGB="#0000FF"/>
        <Color Name="Yellow" Value="128" RGB="#FFFF00"/>
        <Color Name="Magenta" Value="160" RGB="#FF00FF"/>
        <Color Name="Cyan" Value="192" RGB="#00FFFF"/>
      </ColorWheel>
      <GoboWheel Name="Gobo Wheel 1">
        <Gobo Name="Open" Value="0"/>
        <Gobo Name="Gobo 1" Value="32"/>
        <Gobo Name="Gobo 2" Value="64"/>
        <Gobo Name="Gobo 3" Value="96"/>
        <Gobo Name="Gobo 4" Value="128"/>
      </GoboWheel>
    </FixtureType>
  </Info>
</MA>'''
    
    with open('sample_ma_fixtures.xml', 'w', encoding='utf-8') as f:
        f.write(sample_xml)
    
    print("✅ 已创建示例MA XML文件: sample_ma_fixtures.xml")

if __name__ == "__main__":
    # 创建示例文件
    create_sample_ma_xml()
    
    # 测试解析器
    parser = MAFixtureParser()
    
    # 解析示例文件
    if parser.parse_ma_xml_library('sample_ma_fixtures.xml'):
        print("\n📊 解析统计:")
        stats = parser.get_statistics()
        print(f"  总灯具数: {stats['total_fixtures']}")
        print(f"  制造商数: {stats['manufacturer_count']}")
        print(f"  制造商列表: {', '.join(stats['manufacturers'])}")
        
        print("\n🔍 灯具列表:")
        for fixture_id in parser.list_all_fixtures():
            fixture = parser.get_fixture_info(fixture_id)
            print(f"  {fixture['manufacturer']} - {fixture['name']} ({fixture['channel_count']}CH)")
        
        # 导出为JSON
        parser.export_to_json('ma_fixtures_converted.json')
    else:
        print("❌ 解析失败")
