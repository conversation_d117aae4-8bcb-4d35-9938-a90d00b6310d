# 🎭 专业灯光协议使用指南

## 📋 功能概述

多媒体演出控制中心现已集成完整的专业灯光协议支持，包括：

- **Art-Net 协议** - 以太网灯光控制协议
- **sACN (E1.31) 协议** - 流媒体ACN协议  
- **OSC 协议** - 开放声音控制协议
- **MIDI Show Control** - MIDI演出控制协议
- **专业灯具库管理** - 完整的灯具数据库
- **协议状态监控** - 实时协议状态跟踪

## 🚀 快速开始

### 1. 访问协议管理界面

1. 启动多媒体演出控制中心
2. 登录系统（默认用户名：admin，密码：admin123）
3. 点击 **"高级功能"** 标签页
4. 在协议管理区域中配置各种协议

### 2. 配置Art-Net协议

#### 基本设置
- **Universe**: 设置DMX Universe编号 (1-32768)
- **Subnet**: 设置子网编号 (0-15)
- **Net**: 设置网络编号 (0-127)
- **广播IP**: 设置广播地址 (默认: ***************)

#### 使用步骤
1. 在Art-Net配置区域填写参数
2. 点击 **"启用"** 开关激活协议
3. 点击 **"🧪 测试Art-Net"** 发送测试数据
4. 点击 **"📊 监控数据"** 查看发送统计

### 3. 配置sACN协议

#### 基本设置
- **Universe**: 设置Universe编号 (1-63999)
- **Priority**: 设置优先级 (0-200，默认100)
- **Source Name**: 设置源名称标识

#### 使用步骤
1. 配置sACN参数
2. 启用sACN协议
3. 测试数据包发送
4. 监控发送状态

### 4. 配置OSC协议

#### 基本设置
- **监听端口**: 本地监听端口 (默认: 8000)
- **目标IP**: 目标设备IP地址
- **目标端口**: 目标设备端口 (默认: 9000)

#### 使用步骤
1. 设置OSC通信参数
2. 启用OSC协议
3. 发送测试OSC消息
4. 监控消息传输

### 5. 配置MIDI Show Control

#### 基本设置
- **Device ID**: 设备ID (0-127)
- **Command Format**: 命令格式选择
  - All Call (127) - 全局调用
  - Lighting (1) - 灯光设备
  - Sound (2) - 音响设备
  - Video (3) - 视频设备

## 💡 灯具库管理

### 访问灯具库

1. 在协议管理界面找到 **"💡 灯具库管理"** 区域
2. 点击 **"📚 灯具库"** 打开灯具库管理器

### 灯具库功能

#### 查看和搜索
- **分类筛选**: 按灯具类型筛选
- **搜索功能**: 按名称或制造商搜索
- **详细信息**: 查看灯具规格和DMX模式

#### 添加自定义灯具
1. 点击 **"➕ 添加灯具"** 按钮
2. 填写灯具基本信息：
   - 灯具名称
   - 制造商
   - 分类
   - 通道数
   - 描述
3. 配置DMX模式：
   - 添加多个DMX模式
   - 设置每个模式的通道数
4. 选择支持的协议
5. 保存灯具数据

#### 编辑现有灯具
1. 在灯具列表中点击 **"✏️"** 编辑按钮
2. 修改灯具信息
3. 更新DMX模式配置
4. 保存更改

#### 导入/导出功能
- **导入灯具库**: 从JSON文件导入灯具数据
- **导出灯具库**: 将灯具库导出为JSON文件
- **GDTF支持**: 未来将支持GDTF标准文件导入

## 📊 协议监控

### 实时状态监控

每个协议都有实时状态显示：
- **启用状态**: 协议是否已激活
- **发送计数**: 已发送的数据包数量
- **最后发送时间**: 最近一次发送的时间

### 监控窗口功能

点击 **"📊 监控数据"** 按钮可以：
- 查看详细的协议统计信息
- 刷新实时状态
- 清除统计数据
- 监控协议性能

## 🧪 协议测试

### Art-Net测试
- 发送包含10个通道全亮的测试数据包
- 验证网络连接和设备响应
- 检查Universe配置是否正确

### sACN测试
- 发送标准sACN测试数据包
- 验证组播网络配置
- 测试优先级设置

### OSC测试
- 发送包含浮点数和字符串的测试消息
- 验证OSC通信路径
- 测试消息格式兼容性

### MSC测试
- 发送GO命令测试
- 验证MIDI设备连接
- 测试设备ID配置

## ⚠️ 注意事项

### 网络配置
1. **防火墙设置**: 确保相关端口未被阻止
   - Art-Net: UDP 6454
   - sACN: UDP 5568
   - OSC: 自定义端口
2. **网络权限**: 确保程序有网络发送权限
3. **组播支持**: sACN需要网络支持组播

### 设备兼容性
1. **Art-Net设备**: 确保设备支持Art-Net协议
2. **sACN设备**: 验证设备的sACN兼容性
3. **OSC设备**: 确认OSC消息格式匹配
4. **MIDI设备**: 检查MSC命令格式支持

### 性能优化
1. **数据包频率**: 避免过于频繁的数据发送
2. **网络负载**: 监控网络带宽使用
3. **设备响应**: 观察设备响应时间

## 🔧 故障排除

### 常见问题

#### Art-Net无响应
1. 检查IP地址和端口配置
2. 验证网络连接
3. 确认设备Art-Net设置
4. 检查Universe编号匹配

#### sACN连接失败
1. 验证组播网络支持
2. 检查Universe范围 (1-63999)
3. 确认设备sACN配置
4. 测试网络组播功能

#### OSC通信异常
1. 验证IP地址和端口
2. 检查OSC消息格式
3. 确认设备OSC支持
4. 测试网络连通性

#### MSC命令无效
1. 检查Device ID设置
2. 验证Command Format
3. 确认MIDI连接
4. 测试设备MSC支持

### 调试工具

1. **协议监控窗口**: 查看发送统计
2. **测试功能**: 验证协议工作状态
3. **日志输出**: 查看控制台错误信息
4. **网络工具**: 使用Wireshark等工具分析数据包

## 📈 高级功能

### 批量操作
- 同时启用多个协议
- 批量测试所有协议
- 统一监控所有协议状态

### 自动化集成
- 与场景控制系统集成
- 自动协议切换
- 智能设备发现

### 扩展支持
- 自定义协议插件
- 第三方设备驱动
- 协议转换功能

---

## 📞 技术支持

如有问题或需要技术支持，请联系：
- **开发者**: 徐小龙
- **电话**: 15692899229
- **邮箱**: <EMAIL>

**版本**: V16 终极版
**更新日期**: 2025-06-18
